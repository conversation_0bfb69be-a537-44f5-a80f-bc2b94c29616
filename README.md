### Project Setup Guide:

---

**1. <PERSON> Stack**

Before proceeding, ensure that the following software is installed on your local system, as it is required for running the project locally.

- [python v3.11](https://www.python.org/downloads/release/python-31110/)
- [node v19](https://nodejs.org/en/blog/release/v19.6.1)
- [docker](https://www.docker.com/) for creating postgresql db and build & push images (required for abun k8 & production/staging build)
- [k8s](https://kubernetes.io/) for deployments & cronjobs
- [k9s](https://k9scli.io/) for managing k8 cluster
- [minikube](https://minikube.sigs.k8s.io/docs/start/) for local k8s cluster

**2. Create a directory & Clone the repository.**

- `cd path/you/want-to-setup-the-project/ && mkdir dir-name`

- `git clone https://gitlab.com/aminmemon/abundrfbackend.git .`

**3. Create a python virtual environment using below command.**

- `python3.11 -m virtualenv venv`

*Activate your virtual environment*
```bash
# command to activate virtual environment in linux/mac
source venv/bin/activate

# command to activate virtual environment in windows
venv\Scripts\activate
```

*Install required libraries using `pip install -r requirements.txt`*


**4. Create Postgres DB**

```bash
docker run -d \
       --name abun-postgres \
       -e POSTGRES_USER=<user-name> \
       -e POSTGRES_PASSWORD=<your-password> \
       -e POSTGRES_DB=abun_db \
       -v /path/you/want-to-mount:/var/lib/postgresql/data \
       -p 5432:5432 \
       postgres:15.2
```

**5. Django Settings (setup env variables)**

```bash
# Django settings
DEBUG=1
LOG_LEVEL=DEBUG
ALLOWED_HOSTS=localhost,127.0.0.1,<your-name>-drf.abun.com,<your-name>-frontend.abun.com
CORS_ALLOWED_ORIGINS=http://127.0.0.1:3000,http://localhost:3000,http://localhost:3001,https://<your-name>-frontend.abun.com
SECRET_KEY=kT1ox4Bbb8PoUEUWtT42M7Ghelchho1fAThJH2cxFWIWkO4qHPxiD4X-EbJZ0ZvAz5_lRYHcB6Cty_PyIM0OFQ
JWT_SIGNING_KEY=SOw1bf_alpYtFRlbI2yg-aXLTSBe5suoqf7C7gieMGp-ZSl9GkRhlPJV6jOxX6iDEGZ29pz5-m_rKsjG84qH3g
FEEDBACK_EMAIL_HOURS=48
FOUNDERS_EMAIL_AFTER_SIGNUP_MINUTES=10

# Admin
ADMIN_SECRET=LineOfSight

# Sentry Logging
USE_SENTRY=0

# Reset Password
RESET_PASSWORD_ENCRYPTION_KEY=uieAcWtZGAMhazyQ8u5fkieestohWWeWvw_gQaTkRyg=
EMAIL_VERIFICATION_ENCRYPTION_KEY=q6n8EoQdfVpUA83ChMqyiQVq9zVU9DPJNI_AQNAPm04=
RESET_PASSWORD_EXPIRY_HOURS=24
RESET_PASSWORD_LINK_DOMAIN=http://localhost:3000

# Database (postgres) config
DB_NAME=<db-name-while-creating-the-postgres-db>
DB_USER=<db-user-while-creating-the-postgres-db>
DB_PASSWORD=<db-password-while-creating-the-postgres-db>
DB_HOST=localhost
DB_PORT=5432

# Kubernetes
K8_NAMESPACE=<your-name>
K8_IMAGE_TAG=dev-<your-name>
K8_CONFIG_PATH=/path/to/.kube/config

# OpenAI
OPENAI_API_KEY=  # ask us
MINI_AI_TOOL_OPENAI_API_KEY=  # ask us

# Redis
# make sure port forwarding is running
# kubectl port-forward --namespace arun svc/abun-redis-master 6378:6379
REDIS_HOST=localhost
REDIS_PORT=6378
REDIS_TASK_DATA_DB=0
REDIS_STRIPE_DB=1
REDIS_CONTENT_PLAN_EXPIRY=86400
REDIS_ART_GEN_EXPIRY=28800

# AWS
AWS_ACCESS_KEY_ID=  # ask us
AWS_SECRET_ACCESS_KEY=  # ask us

# Stripe
STRIPE_API_KEY=sk_test_51NhU8USAw4DIVBDnfna2VRG9AyDn7UpMcYgtaU3IAem3fBsiLKAnwUfD9SRoRfJL5zjlHm1jpLW08Bu1HtqvA9Cj00ZCwj89PX
STRIPE_ENDPOINT_SECRET=whsec_3BpIGEcgZUT2zRp5o4tZEFRpHtIP6jL9
STRIPE_CUSTOMER_PORTAL_LINK=https://billing.stripe.com/p/login/test_bIYcMZ3Wj6K371S000
STRIPE_CUSTOMER_PORTAL_RETURN_URL=http://localhost:3000/manage-subscription

# Wordpress Integration
WP_RETURN_URL_DOMAIN=http://localhost:3000
WP_DOMAIN_ENCRYPTION_KEY=lrFbX5UbEwZfVYFkRBKT-j3zOtgiWR5ar2_6cTQUurQ=

# Cloudflare (R2)
CLOUDFLARE_ACCOUNT_ID=731fd8c5abc3f4cf167f59ad8f5e341e
CLOUDFLARE_R2_ACCESS_KEY=RHFITCG16B7197K2FB56
CLOUDFLARE_R2_SECRET_KEY=****************************************
CLOUDFLARE_R2_BUCKET_NAME=abun-media
CLOUDFLARE_R2_DOMAIN=cdn.abun.com
CLOUDFLARE_R2_ENV=dev

# Celery
CELERY_TIMEZONE=UTC
CELERY_BROKER_URL=redis://localhost:6378/2
CELERY_RESULT_BACKEND=redis://localhost:6378/2
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_MAX_MEMORY_PER_CHILD=100000

# To allow Http traffic for local dev (google oauth)
OAUTHLIB_INSECURE_TRANSPORT=1


# AI Tools
REDIS_MINI_AI_TOOL_EXPIRY=2000
REDIS_MINI_AI_TOOL_DB=2

# DEEP Infra API Token for AI image generation
DEEPINFRA_API_TOKEN=  # ask us

# DATAFORSEO
DATAFORSEO_LOGIN=<EMAIL>
DATAFORSEO_PASSWORD=  # ask us

# KEYWORDSEVERYWHERE
KEYWORDSEVERYWHERE_API_KEY=  # ask us

# SEGMIND API
SEGMIND_API_KEY=  # ask us

# GOOGLE SERPER API
GOOGLE_SERPER_API_KEY=  # ask us

# Shopify API Version
SHOPIFY_API_VERSION=2024-10

# ADMIN
ADMIN_DOMAIN=http://localhost:3001

# AppSumo (Oauth keys)
APPSUMO_CLIENT_ID=not-required-for-development
APPSUMO_CLIENT_SECRET=not-required-for-development
APPSUMO_PRIVATE_KEY=not-required-for-development

# HuggingFace API KEY
HF_API_KEY=  # ask us

# ChromaDB
CHROMADB_HOST=localhost
CHROMADB_PORT=8000

# Fly.io For Article Generation
FLY_ARTICLE_GEN_DEPLOY_TOKEN=not-required-for-development
FLY_ARTICLE_GEN_APP_NAME=not-required-for-development

# Fly.io For Website Scanning
FLY_WEBSITE_SCANNING_DEPLOY_TOKEN=not-required-for-development
FLY_WEBSITE_SCANNING_APP_NAME=not-required-for-development

# Fly.io For Competitor Finder v2
FLY_COMPETITOR_FINDER_DEPLOY_TOKEN=not-required-for-development
FLY_COMPETITOR_FINDER_APP_NAME=not-required-for-development

# Fly.io For Article Internal Linking
FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN=not-required-for-development
FLY_ARTICLE_INTERNAL_LINK_APP_NAME=not-required-for-development

# PEXELS API KEY
PEXELS_API_KEY=  # ask us

# GHL Integration
GHL_CLIENT_ID=  # ask us
GHL_CLIENT_SECRET=  # ask us

# REDDIT API KEY
REDDIT_SECRET_KEY= # ask us
REDDIT_CLIENT_KEY= # ask us

```

**6. Run migrate command and run the development server**

**Migrate modals using migrate command `python manage.py migrate`**

**Run Django server `python manage.py runserver`**

**7. Start Minikube Cluster**

**Execute the command `minikube start`. This process may take some time. Once completed, your current context should be set to `minikube`. You can verify this by running `kubectl config current-context`.**

**8. Create Namespace**

**`kubectl create namespace <namespace-name>` (replace `<namespace-name>` with the value specified in your DRF `.env` file)**

**9. Deploy Redis**

**In the DRF repository, navigate to `K8/dev` and open the `deploy-redis.dev.sh` script. Update the following variables:**

 - Set `CONTEXT_NAME=minikube`
 - Set `NAMESPACE` to the value specified in your DRF `.env` file.

*Run the Redis Deployment Script*
```
./deploy-redis.dev.sh
```

*Run the below command to remove redis*
```
helm delete abun-redis --namespace yash
```

**Once redis is deployed run `kubectl port-forward --namespace <NAMESPACE> svc/abun-redis-master 6378:6379` command to bind the redis port.**

**10. Deploy ChromaDB**

**In the DRF repository, navigate to `K8/dev` and run the ChromaDB deployment script:**

```
./deploy-chroma-dev.sh -n <namespace-name>
```

*Replace `<namespace-name>` with the value specified in your DRF `.env` file.*

**Once ChromaDB is deployed, run the following command to bind the ChromaDB port:**

```
kubectl port-forward --namespace <NAMESPACE> svc/abun-chromadb-service-dev 8000:8000
```

*Run the below command to remove ChromaDB if needed:*
```
kubectl delete deployment abun-chromadb-deployment -n <NAMESPACE>
kubectl delete service abun-chromadb-service-dev -n <NAMESPACE>
kubectl delete pvc abun-chromadb-pvc-dev -n <NAMESPACE>
```

**11. Run Celery worker**

`celery -A AbunDRFBackend worker --loglevel=info`

**12. Create an Access Token on GitLab with Read & Write Registry Permissions**

- Navigate to User Settings
  - In the upper-right corner of the GitLab interface, click on your profile picture or avatar.
  - From the dropdown menu, select "Edit profile".

- Access the Access Tokens Section
  - In the left sidebar, click on "Access Tokens". This will take you to the page where you can create new personal access tokens.

- Create a New Access Token
  - **Name your token**: In the **"Name"** field, enter a descriptive name for your access token `(e.g., Abun Dev Access Token)`.
  - **Set an expiration date** (optional): You can set an expiration date for the token. If you want the token to be valid indefinitely, you can leave this field blank.
  - **Select Scopes**: Check the following scopes to grant the necessary permissions.
    1. **`read_registry`**: Allows reading from the container registry.
    2. **`write_registry`**: Allows writing to the container registry.

- Save Your Access Token
  - After creating the token, GitLab will display it on the screen. **Make sure to copy this token** and store it in a secure location, as you will not be able to see it again once you navigate away from the page.

**13. Log in to the GitLab container registry**

**Use your access token to authenticate with the GitLab container registry.**

```
docker login registry.gitlab.com -u <your_gitlab_username> -p <your_access_token>
```

**14. Add Container Registry Secret**

**You can use `add-container-registry-secret.sh` script located in the `K8` directory of the DRF repository to add the registry secret. Run the following command:**

```
./add-container-registry-secret.sh registry.gitlab.com <username> <password> <email> gitlab-registry-credentials <namespace>
```

  - Replace `<username>` with your GitLab username.
  - Replace `<password>` with just created access token on GitLab.
  - Replace `<namespace>` with the value specified in your DRF `.env` file.


**15. Build and Push The Images**

On [K8 repository](https://gitlab.com/aminmemon/abunk8) you can use the `build-and-push-image.sh` script to build & push the images. Run `./build-and-push-image` and build all the images. Use -t flag to specify the tag.

Example:
```
./build-and-push-image -t dev-<your-name>
```


**16. Create Development Secrets**

**Open the `secrets.dev.yml` file located in the root directory of the [K8 repository](https://gitlab.com/aminmemon/abunk8). Update the `namespace` and `ABUN_K8_DRF_DOMAIN` values according to your DRF `.env` file. After making the necessary updates, run the command:**

```
kubectl apply -f secrets.dev.yml
```

**17. Try generating an Article**

**Once everything is setup & running, try generating an article. You can monitor the article generation task using `k9s`**

```
k9s -n <NAMESPACE>
```

- use arrow keys to navigate
- press `enter` open an task & view the logs
- press `esc` to go back

---
### Docker:

---

NOTE: Make sure to run docker build command from the project root

> docker build command: `docker buildx build --platform linux/amd64 -f docker/staging/Dockerfile.staging -t registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:<version> .`


### Reset Password Secret:

Generate the secret used for Reset Password using cryptography library (Fernet):
```python
from cryptography.fernet import Fernet
key = Fernet.generate_key()  # Use this value
```

### Kubernetes:

---

- Download the config file(s) from Linode.
- Create a new namespace for yourselves and add this to the .env file.
- We are using [python kubernetes library](https://github.com/kubernetes-client/python).
- Add your k8 config file path to .env file.

**Installing Metrics Server:** `helm upgrade --install --namespace kube-system -f metrics.yaml metrics-server metrics-server/metrics-server`
(test with `kubectl top node`)


### Staging

- To update **traefik** helm installation with new chart values, run this command: `helm upgrade -n abun-staging -f K8/traefik-chart-values.staging.yml traefik traefik/traefik`


### Production

#### Deployment Steps:

1. Add all secrets and similar resources.
2. Deploy Redis.
3. Apply `pv` yaml.
4. Set up Traefik using Helm: `helm install -n abun-prod -f K8/traefik-chart-values.prod.yml traefik traefik/traefik`.
5. Apply `deployment` yaml.
6. Apply `ingress` yaml.
7. Apply `cronjobs` yaml.

#### Redis:

To connect to your database from outside the cluster execute the following commands:

```kubectl port-forward --namespace abun-prod svc/abun-redis-master 6379:6379```

```redis-cli -h 127.0.0.1 -p 6379```

#### Renew SSL Certificate Using Cerbot

To renew the ssl certificate run the below command on the server

```shell
sudo certbot renew
```

To renew a specific certificate, use:

```shell
certbot certonly --force-renew -d abun.com
```

To verify that the certificate renewed, run:

```shell
sudo certbot renew --dry-run
```
