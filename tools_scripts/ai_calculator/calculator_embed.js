(function (w, d, s, o) {
    // Unique ID for the calculator
    var calculatorId = o.calculatorId || 'default';

    // Look for the target div with id 'abun-ai-calculator'
    var targetDiv = d.getElementById('abun-ai-calculator');

    if (!targetDiv) {
        console.error('Abun AI Calculator: Target div with id "abun-ai-calculator" not found. Please add <div id="abun-ai-calculator"></div> to your page.');
        return;
    }

    // Create the calculator container
    var calculatorContainer = d.createElement('div');
    calculatorContainer.id = 'calculator-widget-' + calculatorId;
    calculatorContainer.style.width = '100%';
    calculatorContainer.style.height = 'auto';
    calculatorContainer.style.minHeight = '400px'; // Ensure minimum height
    calculatorContainer.style.backgroundColor = '#ffffff';
    calculatorContainer.style.borderRadius = '12px';
    calculatorContainer.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    calculatorContainer.style.overflow = 'hidden';
    calculatorContainer.style.border = '1px solid #ddd';
    calculatorContainer.style.position = 'relative';

    // Clear any existing content in the target div and append calculator
    targetDiv.innerHTML = '';
    targetDiv.appendChild(calculatorContainer);

    // Show loading state
    calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #666;">Loading calculator...</div>';

    // Fetch calculator code and inject it directly
    fetch(o.serverUrl + '/api/frontend/get-calculator-code/' + calculatorId)
        .then(function(response) {
            if (!response.ok) {
                throw new Error('Failed to load calculator: ' + response.status);
            }
            return response.text();
        })
        .then(function(htmlContent) {
            // Inject the calculator HTML directly into the container
            calculatorContainer.innerHTML = htmlContent;

            // Execute any scripts in the injected HTML
            var scripts = calculatorContainer.querySelectorAll('script');
            for (var i = 0; i < scripts.length; i++) {
                var script = scripts[i];
                var newScript = d.createElement('script');

                if (script.src) {
                    newScript.src = script.src;
                } else {
                    newScript.textContent = script.textContent;
                }

                // Copy any attributes
                for (var j = 0; j < script.attributes.length; j++) {
                    var attr = script.attributes[j];
                    if (attr.name !== 'src') {
                        newScript.setAttribute(attr.name, attr.value);
                    }
                }

                // Replace the old script with the new one to ensure execution
                script.parentNode.replaceChild(newScript, script);
            }
        })
        .catch(function(error) {
            console.error('Abun AI Calculator: Error loading calculator:', error);
            calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #e74c3c;">Error loading calculator. Please try again later.</div>';
        });

    // Expose API for external control
    w.CustomCalculator = w.CustomCalculator || {};
    w.CustomCalculator[calculatorId] = {
        show: function () {
            calculatorContainer.style.display = 'block';
        },
        hide: function () {
            calculatorContainer.style.display = 'none';
        },
        reload: function () {
            // Reload the calculator by fetching fresh content
            calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #666;">Reloading calculator...</div>';

            fetch(o.serverUrl + '/api/frontend/get-calculator-code/' + calculatorId)
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Failed to reload calculator: ' + response.status);
                    }
                    return response.text();
                })
                .then(function(htmlContent) {
                    calculatorContainer.innerHTML = htmlContent;

                    // Execute any scripts in the reloaded HTML
                    var scripts = calculatorContainer.querySelectorAll('script');
                    for (var i = 0; i < scripts.length; i++) {
                        var script = scripts[i];
                        var newScript = d.createElement('script');

                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }

                        // Copy any attributes
                        for (var j = 0; j < script.attributes.length; j++) {
                            var attr = script.attributes[j];
                            if (attr.name !== 'src') {
                                newScript.setAttribute(attr.name, attr.value);
                            }
                        }

                        script.parentNode.replaceChild(newScript, script);
                    }
                })
                .catch(function(error) {
                    console.error('Abun AI Calculator: Error reloading calculator:', error);
                    calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #e74c3c;">Error reloading calculator. Please try again later.</div>';
                });
        },
        getContainer: function () {
            return calculatorContainer;
        }
    };

})(window, document, 'script', {
    calculatorId: 'CALCULATOR_ID',
    serverUrl: 'SERVER_URL',
    buttonColor: 'BUTTON_COLOR'
});