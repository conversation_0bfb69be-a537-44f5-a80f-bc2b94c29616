(function(w, d, s, o) {
    var jsonLdString = o.jsonLd;
    var scriptId = 'jsonld-schema-script';

    // Avoid adding duplicate script
    if (d.getElementById(scriptId)) {
        console.warn('JSON-LD schema already injected.');
        return;
    }

    try {
        // Parse to validate JSON
        var parsedJson = JSON.parse(jsonLdString);

        // Create script tag
        var scriptTag = d.createElement('script');
        scriptTag.type = 'application/ld+json';
        scriptTag.id = scriptId;
        scriptTag.text = JSON.stringify(parsedJson, null, 2);

        // Inject into head
        d.head.appendChild(scriptTag);
    } catch (e) {
        console.error('❌ Invalid JSON-LD schema provided:', e.message);
    }
})(window, document, 'script', {
    jsonLd: 'JSONLD_SCHEMA'
});
