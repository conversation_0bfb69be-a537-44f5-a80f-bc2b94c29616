apiVersion: v1
kind: PersistentVolume
metadata:
  name: tls-certificate-store-volume
  namespace: abun-prod
  labels:
    type: local
spec:
  storageClassName: linode-block-storage-retain
  capacity:
    storage: 128Mi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/certdata"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: chromadb-store-volume-date
  namespace: abun-prod
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: standard
  hostPath:
    path: "/abun_chroma_db_data"

---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chromadb-store-volume-data-claim
  namespace: abun-prod
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
