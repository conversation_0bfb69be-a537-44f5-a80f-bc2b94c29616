apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: abun-traefik-http-ingress-route
  namespace: abun-prod
spec:
  entryPoints:
    - web
    - websecure
  routes:
  - match: Host(`app.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
    - name: abun-react-service
      namespace: abun-prod
      port: 3000
  - match: Host(`api.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
      - name: abun-drf-service
        namespace: abun-prod
        port: 8000
  - match: Host(`admin.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
      - name: abun-admin-service
        namespace: abun-prod
        port: 3001
