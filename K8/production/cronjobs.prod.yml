apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-admin-stats-cron
  namespace: abun-prod
spec:
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-admin-stats-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "capture_stats_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

# apiVersion: batch/v1
# kind: CronJob
# metadata:
#   name: abun-feedback-email-email-cron
#   namespace: abun-prod
# spec:
#   schedule: "*/30 * * * *"
#   concurrencyPolicy: Forbid
#   successfulJobsHistoryLimit: 1
#   failedJobsHistoryLimit: 1
#   timeZone: "Etc/UTC"
#   jobTemplate:
#     spec:
#       template:
#         spec:
#           containers:
#             - name: abun-feedback-email-container
#               image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
#               imagePullPolicy: IfNotPresent
#               command: ["python", "manage.py", "feedback_email_cronjob"]
#               envFrom:
#                 - secretRef:
#                     name: abun-app-env-variables
#           imagePullSecrets:
#             - name: gitlab-registry-credentials
#           restartPolicy: OnFailure

# ---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-founders-email-email-cron
  namespace: abun-prod
spec:
  schedule: "*/10 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-founders-email-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "founders_email_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-stuck-process-check-cron
  namespace: abun-prod
spec:
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-stuck-process-check-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "stuck_process_check_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-publish-scheduled-articles-cron
  namespace: abun-prod
spec:
  schedule: "*/5 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-publish-scheduled-articles-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "publish_scheduled_articles_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-article-email-cron
  namespace: abun-prod
spec:
  schedule: "*/30 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-article-email-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "article_email_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-no-plan-selection-cron
  namespace: abun-prod
spec:
  schedule: "*/30 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-no-plan-selection-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "no_plan_selection_email_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-reset-ltd-plan-usage-cron
  namespace: abun-prod
spec:
  schedule: "0 0 * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-reset-ltd-plan-usage-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "reset_ltd_plan_usage_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-process-failed-article-webhooks-cron
  namespace: abun-prod
spec:
  schedule: "* * * * *"  # Every minute
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-process-failed-article-webhooks-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "process_failed_article_generation_webhooks"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-store-website-indexation-details-cron
  namespace: abun-prod
spec:
  schedule: "0 0 * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-store-website-indexation-details-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "store_website_indexation_details_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure

---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: abun-auto-indexing-cron
  namespace: abun-prod
spec:
  schedule: "0 0 * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Etc/UTC"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: abun-auto-indexing-container
              image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.77.4
              imagePullPolicy: IfNotPresent
              command: ["python", "manage.py", "auto_indexing_cronjob"]
              envFrom:
                - secretRef:
                    name: abun-app-env-variables
          imagePullSecrets:
            - name: gitlab-registry-credentials
          restartPolicy: OnFailure
