CONTEXT_NAME="abun-production"
NAMESPACE="abun-prod"

# Check current context.
if [[ $(kubectl config current-context) != "$CONTEXT_NAME" ]]; then
  echo "Incorrect context name. Please awitch to ${CONTEXT_NAME} and try again."
  exit 1
fi

# Check if <PERSON><PERSON> is installed.
if ! helm version > /dev/null; then
  echo "Could not find 'helm' command. Please install Helm (https://helm.sh/docs/intro/install/) or make sure 'helm' command is on the PATH"
  exit 1
fi

# Deploy redis
printf "\n[4/5] Deploying Red<PERSON> through helm. This might take a few minutes..."
helm install \
  --namespace $NAMESPACE \
  --set name=abun-redis \
  --set architecture=standalone \
  --set auth.enabled=false \
  --set master.resources.limits.cpu=600m \
  --set master.resources.limits.memory=1024Mi \
  --set master.resources.requests.cpu=300m \
  --set master.resources.requests.memory=512Mi \
  abun-redis \
  oci://registry-1.docker.io/bitnamicharts/redis \
  || { echo "**** DEPLOYMENT FAILED ****"; exit 1; }
