version: "3.8"

services:
  redis-master:
    image: redis:latest
    container_name: redis-master
    hostname: redis-master
    ports:
      - "6379:6379"
    volumes:
      - ./data/master:/data
    command:
      [
        "redis-server",
        "--appendonly",
        "yes",
        "--repl-diskless-load",
        "on-empty-db",
        "--replica-announce-ip",
        "${HOST_IP}",
        "--replica-announce-port",
        "6379",
        "--protected-mode",
        "no",
        "--requirepass",
        "yP1sYGQBT4UEdOR9A10G"
      ]
    networks:
      redis-net:
        ipv4_address: **********


  redis-slave-1:
    image: redis:latest
    container_name: redis-slave-1
    hostname: redis-slave-1
    depends_on:
      - redis-master
    ports:
      - "6380:6379"
    volumes:
      - ./data/slave1:/data
    command:
      [
        "redis-server",
        "--appendonly",
        "yes",
        "--replicaof",
        "redis-master",
        "6379",
        "--repl-diskless-load",
        "on-empty-db",
        "--replica-announce-ip",
        "${HOST_IP}",
        "--replica-announce-port",
        "6380",
        "--protected-mode",
        "no",
        "--masterauth",
        "yP1sYGQBT4UEdOR9A10G",
        "--requirepass",
        "yP1sYGQBT4UEdOR9A10G"
      ]
    networks:
      redis-net:
        ipv4_address: **********


  redis-slave-2:
    image: redis:latest
    container_name: redis-slave-2
    hostname: redis-slave-2
    depends_on:
      - redis-master
    ports:
      - "6381:6379"
    volumes:
      - ./data/slave2:/data
    command:
      [
        "redis-server",
        "--appendonly",
        "yes",
        "--replicaof",
        "redis-master",
        "6379",
        "--repl-diskless-load",
        "on-empty-db",
        "--replica-announce-ip",
        "${HOST_IP}",
        "--replica-announce-port",
        "6381",
        "--protected-mode",
        "no",
        "--masterauth",
        "yP1sYGQBT4UEdOR9A10G",
        "--requirepass",
        "yP1sYGQBT4UEdOR9A10G"
      ]
    networks:
      redis-net:
        ipv4_address: **********


  sentinel-1:
    image: redis:latest
    container_name: sentinel-1
    hostname: sentinel-1
    depends_on:
      - redis-master
    ports:
      - "26379:26379"
    command: >
      sh -c 'echo "bind 0.0.0.0" > /etc/sentinel.conf &&
            echo "sentinel monitor abun-redis-master ${HOST_IP} 6379 2" >> /etc/sentinel.conf &&
            echo "sentinel resolve-hostnames yes" >> /etc/sentinel.conf &&
            echo "sentinel down-after-milliseconds abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel failover-timeout abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel parallel-syncs abun-redis-master 1" >> /etc/sentinel.conf &&
            echo "sentinel auth-pass abun-redis-master yP1sYGQBT4UEdOR9A10G" >> /etc/sentinel.conf &&
            redis-sentinel /etc/sentinel.conf'
    networks:
      redis-net:
        ipv4_address: **********


  sentinel-2:
    image: redis:latest
    container_name: sentinel-2
    hostname: sentinel-2
    depends_on:
      - redis-master
    ports:
      - "26380:26379"
    command: >
      sh -c 'echo "bind 0.0.0.0" > /etc/sentinel.conf &&
            echo "sentinel monitor abun-redis-master ${HOST_IP} 6379 2" >> /etc/sentinel.conf &&
            echo "sentinel resolve-hostnames yes" >> /etc/sentinel.conf &&
            echo "sentinel down-after-milliseconds abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel failover-timeout abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel parallel-syncs abun-redis-master 1" >> /etc/sentinel.conf &&
            echo "sentinel auth-pass abun-redis-master yP1sYGQBT4UEdOR9A10G" >> /etc/sentinel.conf &&
            redis-sentinel /etc/sentinel.conf'
    networks:
      redis-net:
        ipv4_address: **********

  sentinel-3:
    image: redis:latest
    container_name: sentinel-3
    hostname: sentinel-3
    depends_on:
      - redis-master
    ports:
      - "26381:26379"
    command: >
      sh -c 'echo "bind 0.0.0.0" > /etc/sentinel.conf &&
            echo "sentinel monitor abun-redis-master ${HOST_IP} 6379 2" >> /etc/sentinel.conf &&
            echo "sentinel resolve-hostnames yes" >> /etc/sentinel.conf &&
            echo "sentinel down-after-milliseconds abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel failover-timeout abun-redis-master 10000" >> /etc/sentinel.conf &&
            echo "sentinel parallel-syncs abun-redis-master 1" >> /etc/sentinel.conf &&
            echo "sentinel auth-pass abun-redis-master yP1sYGQBT4UEdOR9A10G" >> /etc/sentinel.conf &&
            redis-sentinel /etc/sentinel.conf'
    networks:
      redis-net:
        ipv4_address: **********

  # redisinsight:
  #   image: redis/redisinsight:latest
  #   container_name: redisinsight
  #   ports:
  #     - "5540:5540"
  #   networks:
  #     redis-net:
  #       ipv4_address: **********

networks:
  redis-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16