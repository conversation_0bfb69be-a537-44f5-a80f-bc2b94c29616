#!/bin/bash

# Function to show usage
show_usage() {
    echo "Usage: $0 -n <NAMESPACE>"
    echo ""
    echo "Options:"
    echo "  -n    Kubernetes namespace to deploy ChromaDB to"
    echo ""
    echo "Example:"
    echo "  $0 -n yash"
    exit 1
}

# Parse command line arguments
NAMESPACE=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -n)
            NAMESPACE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            ;;
    esac
done

# Validate required arguments
if [ -z "$NAMESPACE" ]; then
    echo "Error: -n argument is required"
    show_usage
fi

CONTEXT_NAME="minikube"

# Check current context
if [[ $(kubectl config current-context) != "$CONTEXT_NAME" ]]; then
  echo "Incorrect context name. Please switch to '$CONTEXT_NAME' and try again."
  exit 1
fi

echo "Deploying ChromaDB to development environment..."
echo "Namespace: $NAMESPACE"

# Get the absolute path to the chroma_db directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
CHROMA_DB_PATH="$WORKSPACE_DIR/chroma_db"
MINIKUBE_MOUNT_PATH="/mnt/chroma_db"

echo "Workspace directory: $WORKSPACE_DIR"
echo "Local ChromaDB data path: $CHROMA_DB_PATH"
echo "Minikube mount path: $MINIKUBE_MOUNT_PATH"

# Function to replace placeholders in manifest files
replace_placeholders() {
    local file="$1"
    local temp_file="${file}.tmp"
    
    # Replace namespace and path placeholders
    sed "s/<YOUR-NAMESPACE>/$NAMESPACE/g; s|<CHROMA-DB-PATH>|$MINIKUBE_MOUNT_PATH|g" "$file" > "$temp_file"
    mv "$temp_file" "$file"
}

# Function to revert placeholders in manifest files
revert_placeholders() {
    local file="$1"
    local temp_file="${file}.tmp"
    
    # Revert namespace and path placeholders
    sed "s/$NAMESPACE/<YOUR-NAMESPACE>/g; s|$MINIKUBE_MOUNT_PATH|<CHROMA-DB-PATH>|g" "$file" > "$temp_file"
    mv "$temp_file" "$file"
}

# Trap to ensure files are reverted on exit
cleanup() {
    echo "Reverting manifest files to original state..."
    revert_placeholders "chromadb.deployment.dev.yml"
    revert_placeholders "chromadb.service.dev.yml"
    revert_placeholders "chromadb.pvc.dev.yml"
    if [ -d "$CHROMA_DB_PATH" ]; then
        revert_placeholders "chromadb.hostpath.pv.dev.yml"
    fi
}
trap cleanup EXIT

# Replace placeholders in manifest files
echo "Preparing manifest files with namespace: $NAMESPACE"
replace_placeholders "chromadb.deployment.dev.yml"
replace_placeholders "chromadb.service.dev.yml"
replace_placeholders "chromadb.pvc.dev.yml"

# Check if chroma_db directory exists
if [ -d "$CHROMA_DB_PATH" ]; then
  echo "✓ chroma_db/ directory found. Preparing hostPath volume mount."

  # Start Minikube mount in background if not already running
  mount_running=$(pgrep -f "minikube mount $CHROMA_DB_PATH:$MINIKUBE_MOUNT_PATH")
  if [ -z "$mount_running" ]; then
    echo "⏳ Starting minikube mount in background: $CHROMA_DB_PATH -> $MINIKUBE_MOUNT_PATH"
    nohup minikube mount "$CHROMA_DB_PATH:$MINIKUBE_MOUNT_PATH" >/dev/null 2>&1 &
    sleep 2
  else
    echo "✓ Minikube mount already running"
  fi

  # Replace and apply hostPath PV
  replace_placeholders "chromadb.hostpath.pv.dev.yml"
  echo "Applying hostPath PersistentVolume..."
  kubectl apply -f chromadb.hostpath.pv.dev.yml || { echo "✗ Failed to apply hostPath PV"; exit 1; }
  echo "✓ hostPath PV applied successfully"
else
  echo "ℹ No chroma_db/ directory found at $CHROMA_DB_PATH. ChromaDB will start with empty data."
fi

# Apply PVC
echo "Applying PersistentVolumeClaim..."
kubectl apply -f chromadb.pvc.dev.yml || { echo "✗ Failed to apply PVC"; exit 1; }
echo "✓ PVC applied successfully"

# Apply Service
echo "Applying ChromaDB Service..."
kubectl apply -f chromadb.service.dev.yml || { echo "✗ Failed to apply Service"; exit 1; }
echo "✓ Service applied successfully"

# Apply Deployment
echo "Applying ChromaDB Deployment..."
kubectl apply -f chromadb.deployment.dev.yml || { echo "✗ Failed to apply Deployment"; exit 1; }
echo "✓ Deployment applied successfully"

# Wait for deployment to be ready
echo "Waiting for ChromaDB deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/abun-chromadb-deployment -n $NAMESPACE || { echo "✗ Deployment failed to become ready"; exit 1; }

echo "✅ ChromaDB deployment to development completed successfully!"
echo ""
echo "ChromaDB server is now running at: abun-chromadb-service-dev.$NAMESPACE.svc.cluster.local"
echo ""
echo "To check the status:"
echo "  kubectl get pods -n $NAMESPACE -l app=chromadb-dev"
echo "  kubectl logs -n $NAMESPACE -l app=chromadb-dev"
