apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-chromadb-deployment
  namespace: <YOUR-NAMESPACE>
  labels:
    app: abun-chromadb-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-chromadb-dev
  template:
    metadata:
      labels:
        app: abun-chromadb-dev
    spec:
      containers:
      - name: abun-chromadb-container
        image: chromadb/chroma:1.0.5
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: abun-chromadb-storage
          mountPath: /data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: abun-chromadb-storage
        persistentVolumeClaim:
          claimName: abun-chromadb-pvc-dev
