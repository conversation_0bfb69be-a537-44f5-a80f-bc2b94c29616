#!/bin/bash

CONTEXT_NAME="minikube"
NAMESPACE="priyanshu"

# Check current context.
if [[ $(kubectl config current-context) != "$CONTEXT_NAME" ]]; then
  echo "Incorrect context name. Please awitch to '$CONTEXT_NAME' and try again."
  exit 1
fi

# Check if <PERSON><PERSON> is installed.
if ! helm version > /dev/null; then
  echo "Could not find 'helm' command. Please install <PERSON><PERSON> (https://helm.sh/docs/intro/install/) or make sure 'helm' command is on the PATH"
  exit 1
fi

# Deploy redis
printf "\n[4/5] Deploying Redis through helm. This might take a few minutes..."
helm install \
  --namespace $NAMESPACE \
  --set name=abun-redis \
  --set architecture=standalone \
  --set auth.enabled=false \
  abun-redis \
  oci://registry-1.docker.io/bitnamicharts/redis \
  || { echo "**** DEPLOYMENT FAILED ****"; exit 1; }
