#!/bin/bash

# Function to encode credentials to base64
encode_credentials() {
  echo -n "$1" | base64
}

# Function to create Docker config J<PERSON><PERSON>
create_docker_config_json() {
  cat <<EOF > docker-config.json
{
  "auths": {
    "$1": {
      "username": "$2",
      "password": "$3",
      "email": "$4",
      "auth": "$(encode_credentials "$2:$3")"
    }
  }
}
EOF
}

# Main function
main() {
  if [ $# -ne 6 ]; then
    echo "Usage: $0 <registry-address> <username> <password> <email> <secret-name> <namespace>"
    exit 1
  fi

  REGISTRY_ADDRESS=$1
  USERNAME=$2
  PASSWORD=$3
  EMAIL=$4
  SECRET_NAME=$5
  NAMESPACE=$6

  create_docker_config_json "$REGISTRY_ADDRESS" "$USERNAME" "$PASSWORD" "$EMAIL"

  kubectl create secret generic "$SECRET_NAME" \
    --from-file=.dockerconfigjson=docker-config.json \
    --type=kubernetes.io/dockerconfigjson \
    -n $NAMESPACE

  # Clean up
  rm docker-config.json
}

# Execute main function with arguments
main "$@"
