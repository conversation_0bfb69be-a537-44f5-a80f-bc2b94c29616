apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-chromadb-deployment
  namespace: abun-staging
  labels:
    app: abun-chromadb-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-chromadb-staging
  template:
    metadata:
      labels:
        app: abun-chromadb-staging
    spec:
      containers:
      - name: abun-chromadb-container
        image: chromadb/chroma:1.0.5
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        envFrom:
        - secretRef:
            name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-storage
          mountPath: /data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: chromadb-storage
        persistentVolumeClaim:
          claimName: chromadb-store-volume-claim
