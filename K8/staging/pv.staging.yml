apiVersion: v1
kind: PersistentVolume
metadata:
  name: tls-certificate-store-volume
  namespace: abun-staging
  labels:
    type: local
spec:
  storageClassName: linode-block-storage-retain
  capacity:
    storage: 128Mi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/certdata"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: chromadb-store-volume
  namespace: abun-staging
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: standard
  hostPath:
    path: "/abun_chroma_db"

---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chromadb-store-volume-claim
  namespace: abun-staging
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
