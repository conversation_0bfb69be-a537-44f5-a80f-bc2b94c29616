apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: abun-traefik-http-ingress-route
  namespace: abun-staging
spec:
  entryPoints:
    - web
    - websecure
  routes:
  - match: Host(`staging.app.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
    - name: abun-react-service
      namespace: abun-staging
      port: 3000
  - match: Host(`staging.api.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
      - name: abun-drf-service
        namespace: abun-staging
        port: 8000
  - match: Host(`staging.admin.abun.com`) && PathPrefix(`/`)
    kind: Rule
    services:
      - name: abun-admin-service
        namespace: abun-staging
        port: 3001
