CONTEXT_NAME="abun-dev-and-staging-k8-cluster"
NAMESPACE="abun-staging"

# Check current context.
if [[ $(kubectl config current-context) != "$CONTEXT_NAME" ]]; then
  echo "Incorrect context name. Please switch to ${CONTEXT_NAME} and try again."
  exit 1
fi

# Check if <PERSON><PERSON> is installed.
if ! helm version > /dev/null; then
  echo "Could not find 'helm' command. Please install <PERSON><PERSON> (https://helm.sh/docs/intro/install/) or make sure 'helm' command is on the PATH"
  exit 1
fi

# Deploy redis
printf "\n[4/5] Deploying Red<PERSON> through helm. This might take a few minutes..."
helm install \
  --namespace $NAMESPACE \
  --set name=abun-redis \
  --set architecture=standalone \
  --set auth.enabled=true \
  --set auth.password="xva^b-oT0G+06nWDb=2" \
  --set master.service.type=LoadBalancer \
  --set master.service.ports.redis=6379 \
  abun-redis \
  oci://registry-1.docker.io/bitnamicharts/redis \
  || { echo "**** DEPLOYMENT FAILED ****"; exit 1; }

# Get the external IP
echo "Waiting for external IP to be assigned..."
kubectl get svc abun-redis-master -n $NAMESPACE -w