#deployment:
#  initContainers:
#    # The "volume-permissions" init container is required if you run into permission issues.
#    # Related issue: https://github.com/traefik/traefik-helm-chart/issues/396
#    - name: traefik-pv-permission-fix
#      image: busybox:latest
#      command: [ "sh", "-c", "touch /certdata/acme.json; chmod -v 600 /certdata/acme.json" ]
#      securityContext:
#        runAsNonRoot: true
#        runAsGroup: 65532
#        runAsUser: 65532
#      volumeMounts:
#        - name: tls-certificate-store-volume
#          mountPath: /certdata

## Create an IngressRoute for the dashboard
ingressRoute:
  dashboard:
    # -- Create an IngressRoute for the dashboard
    enabled: true
    # -- Additional ingressRoute annotations (e.g. for kubernetes.io/ingress.class)
    annotations: { }
    # -- Additional ingressRoute labels (e.g. for filtering IngressRoute by custom labels)
    labels: { }
    # -- The router match rule used for the dashboard ingressRoute
    matchRule: PathPrefix(`/dashboard`) || PathPrefix(`/api`)
    # -- Specify the allowed entrypoints to use for the dashboard ingress route, (e.g. traefik, web, websecure).
    # By default, it's using traefik entrypoint, which is not exposed.
    # /!\ Do not expose your dashboard without any protection over the internet /!\
    entryPoints: [ "traefik" ]
    # -- Additional ingressRoute middlewares (e.g. for authentication)
    middlewares: [ ]
    # -- TLS options (e.g. secret containing certificate)
    tls: { }

updateStrategy:
  # -- Customize updateStrategy: RollingUpdate or OnDelete
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 0
    maxSurge: 1

providers:
  kubernetesCRD:
    # -- Load Kubernetes IngressRoute provider
    enabled: true
    # -- Allows IngressRoute to reference resources in namespace other than theirs
    allowCrossNamespace: false
    # -- Allows to reference ExternalName services in IngressRoute
    allowExternalNameServices: false
    # -- Allows to return 503 when there is no endpoints available
    allowEmptyServices: false
    # -- Array of namespaces to watch. If left empty, Traefik watches all namespaces.
    namespaces: [ "abun-staging" ]

# Configure Traefik static configuration
# -- Additional arguments to be passed at Traefik's binary
# All available options available on https://docs.traefik.io/reference/static-configuration/cli/
## Use curly braces to pass values: `helm install --set="additionalArguments={--providers.kubernetesingress.ingressclass=traefik-internal,--log.level=DEBUG}"`
additionalArguments: [ ]
#  - "--providers.kubernetesingress.ingressclass=traefik-internal"
#  - "--log.level=DEBUG"

ports:
  traefik:
    port: 9000
    # -- You SHOULD NOT expose the traefik port on production deployments.
    # If you want to access it from outside your cluster,
    # use `kubectl port-forward` or create a secure ingress
    expose: false
    # -- The exposed port for this service
    exposedPort: 9000
    # -- The port protocol (TCP/UDP)
    protocol: TCP

  web:
    port: 80
    redirectTo: websecure

  websecure:
    port: 443
    tls:
      enabled: true
      # One of the certResolvers defined below
      certResolver: "letsencrypt"
      domains:
        - main: "staging.app.abun.com"
        - main: "staging.api.abun.com"
        - main: "staging.admin.abun.com"

persistence:
  # -- Enable persistence using Persistent Volume Claims
  # ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
  # It can be used to store TLS certificates, see `storage` in certResolvers
  enabled: true
  name: tls-certificate-store-volume
  accessMode: ReadWriteOnce
  size: 128Mi
  volumeName: "tls-certificate-store-volume"
  path: /certdata

# -- Certificates resolvers configuration
certResolvers:
  letsencrypt:
    # for challenge options cf. https://doc.traefik.io/traefik/https/acme/
    email: <EMAIL>
    dnsChallenge:
      # also add the provider's required configuration under env
      # or expand then from secrets/configmaps with envfrom
      # cf. https://doc.traefik.io/traefik/https/acme/#providers
      provider: cloudflare
      # add futher options for the dns challenge as needed
      # cf. https://doc.traefik.io/traefik/https/acme/#dnschallenge
      delayBeforeCheck: 30
      resolvers:
        - 1.1.1.1
        - 8.8.8.8
    tlsChallenge: true
    httpChallenge:
      entryPoint: "web"
    # It has to match the path with a persistent volume
    storage: /certdata/acme.json

# -- Environment variables to be passed to Traefik's binary from configMaps or secrets
envFrom:
  - secretRef:
      name: abun-acme-secrets

# this is required for port 80/443 to work, instead of using 8000 and 8443.
securityContext:
  capabilities:
    drop: [ ALL ]
    add: [ NET_BIND_SERVICE ]
  readOnlyRootFilesystem: true
  runAsGroup: 0
  runAsNonRoot: false
  runAsUser: 0
