# All the prompts used in Abun

## Prompts used in the keywords and content plan generation process

### Prompt to get industry icp (ideal customer profile)

Website title: {title}
Website description: {description}
Website URL: {domain}

{format_instructions}

### Prompt to get competitors domain

Give me 20 valid unique top level domains (example: exmaple.com) of sites providing similar services like {protocol}://{domain}.
{format_instructions}

### Article title generation prompt for content plan

Create the Content Plan as if you are an expert SEO professional.
website: "{domain}"
industry: "{industry}"
ideal customer profile: "{icp}"

Please return 1 long-tail blog title for each Keyword below by following the given Instructions.

Keywords:
{keywords_on_newline}

Instructions:

1. Maintain a counter and increment it by 1 after generating each blog title. Stop generating once this counter reaches {title_count}.
2. Do not add anything before or after the blog titles.
3. Do not include any blog titles that are more than 14 words.
4. Do not use more than 1 verb & 1 adjective in each title.
5. Do not use the keyword in the title as it is.
6. Use simple words in the suggested blog titles.

{format_instructions}

### Keyword details article title generation prompt

Create the Content Plan as if you are an expert SEO professional.
website: "{domain}"
industry: "{industry}"
ideal customer profile: "{icp_text}"

Please return {count} long-tail blog title most relevant to each keyword:
Keyword:
{kewyord}

- Do not add anything before or after the blog titles.
- Do not include any blog titles that are more than 16 words.

Output in the following format:
["title1", "title2", .......]

## All Prompts used for Article Generation

### Article Outline Generation Prompt

As an expert SEO professional, provide 9 main headings and exactly 3 sub headings for each main heading using below Industry, Keyword and Blog Title values. Also, strictly follow given Instructions.

Industry: "{industry}"
Keyword: "{keyword}"
Blog Title: "{title}"

Instructions:

1. Maintain a counter and increment it by 1 after generating each main heading. Stop generating once this counter reaches 9.
2. Do not use words like "Introduction", "Conclusion" or "Key takeaway" in the main heading.
3. Do not add anything before or after the headings.
4. Do not include any main headings that are more than 12 words.
5. Do not include any main headings of listicle, case studies, statistics, templates, examples, showcases, inspirations, and reviews.
6. Add main headings that would contain the most relevant content for "{icp}"

{format_instructions}

### Images generation for Article Prompt

{generated outline}

What is the most important main heading in the above outline that requires a supporting image?
For every 3 main headings, suggest 1 sentence to search on google for a relevant image for a supporting image for the important section.

- Do not add anything before or after the suggestion.
- Do not output any note.

{format_instructions}

### Article Introduction Prompt

Blog Title: "{title}"
Outlines:
{outline}

- Based on the above main outlines, write a 200 word section for "Introduction" in only 1 paragraph.
- Do not add anything before or after the paragraphs.
- Do not add newline character at start or end of paragraph
- Do not self reference in the output.
- Do not add titles or heading at start in the output.
- Limit your answer only for the section required.

### Article Body Content Prompt

Blog Title: "{title}"
Keyword: "{keyword}"

Main Heading: {main_heading}
{sub_headings_in_newline}

Based on the above details,

- Write a section for "{main_heading}" in 3 paragraphs where each paragraph is 500 words.
- Do not add anything before or after the paragraphs.
- Limit your answer only for the section required.
- Do not add headings to paragraph.
{format_instructions}

### Article Body Bold Prompt Text

Blog Text:
{joined_body_paras}

Instructions:
From the above blog text, return a maxiumum of 3 phrases that will make it easier to read between the lines to understand the entire blog post. Do not add anything before or after each phrase. The phrase should not have more than 5 words. Do not suggest phrases with less than 2 words.
{format_instructions}

### Article Conclusion Prompt

Create the Content Plan as if you are an expert SEO professional.
website description: "{description}"

Based on the above details, write a 100 word section for "{domain}" in 1 paragraph pitching {domain} product/services which are most relevant for the target audience "{icp}".

### Article FAQ Prompt

Create the Content Plan as if you are an expert SEO professional.
Blog Title: "{title}"

Based on the blog title, write 5 complex FAQ questions in simple words and their answers. Do not include any questions related to pricing, refunds, policies, terms & conditions. Do not add anything before or after the FAQ. Limit your answer only for the section required.
{format_instructions}

### Article TL;DR Prompt

Blog Title: "{title}"

{body_content_string}

For the above blog, create a TL;DR.

- Do not add anything before or after the TL;DR.
- Limit your answer only for the section required.

Use the following format for output:
TL;DR string

### Article Interlinking Prompt

Blog Title: {title}

Blog article content:

{body_content_string}

In the above blog article, provide the seven most important phrases that can be linked with another article for SEO purposes. 
- Selected phrases should have more than one word but less than ten.
- Do not add anything before or after each phrase.
- Phrases should only contain text.
- Just pick the exact phrase from the article and do not add any extra words.
- Do not include any aesthetically pleasing elements like bullet points, numbered lists, headings or unnecessary capitalization in the phrase.
- Do not pick more than one phrase from the same paragraph or group of paragraphs.
{format_instructions}

## Extras

In this context, `{format_instructions}` refers to a set of instructions or guidelines that are generated by the `output_parser` of langchain language model chain. The `output_parser` is responsible for parsing the output generated by the language model (GPT-4 in our case) and extracting relevant information.
In the code, `{format_instructions}` is a variable that is passed as a partial variable in the prompts.
These formatting instructions could include information on how to structure the headings, how to format the text, or any other specific requirements for generating the outline.
