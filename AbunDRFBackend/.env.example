# Django settings
DEBUG= # use value of 1 only for dev environments. Staging & production environments should not have this variable or else set it to 0.
LOG_LEVEL= # use DEBUG, INFO, WARNING or ERROR
ALLOWED_HOSTS= # comma separated values. ex. localhost,127.0.0.1
CORS_ALLOWED_ORIGINS= # comma separated values but with protocol. ex. http://localhost:3000,http://localhost:7000
SECRET_KEY=  # you can use `secrets.token_urlsafe(64)` to generate one
JWT_SIGNING_KEY=  # you can use `secrets.token_urlsafe(64)` to generate one
FEEDBACK_EMAIL_HOURS=  # Send email asking for feedback after 48 hours of signup
FOUNDERS_EMAIL_AFTER_SIGNUP_MINUTES=  # Send FOUNDERS_EMAIL after 10 minutes of signup

# Reset Password
RESET_PASSWORD_ENCRYPTION_KEY=
RESET_PASSWORD_EXPIRY_HOURS=
RESET_PASSWORD_LINK_DOMAIN=

# Database (postgres) config
DB_NAME=
DB_USER=
DB_PASSWORD=
DB_HOST=
DB_PORT=

# Kubernetes
K8_NAMESPACE= # your kubernetes namespace
K8_IMAGE_TAG= # value should be 'latest' for development, 'staging' for staging and 'production' for production
K8_CONFIG_PATH= # path to kubeconfig .yaml file downloaded from linode

# OpenAI
OPENAI_API_KEY= # openai api key
MINI_AI_TOOL_OPENAI_API_KEY= # openai api key

# Redis
# make sure port forwarding is running on local
# kubectl port-forward --namespace arun svc/abun-redis-master 6378:6379
REDIS_HOST=
REDIS_PORT=
REDIS_TASK_DATA_DB=
REDIS_STRIPE_DB=
REDIS_MINI_AI_TOOL_DB=
REDIS_CONTENT_PLAN_EXPIRY=
REDIS_ART_GEN_EXPIRY=
REDIS_MINI_AI_TOOL_EXPIRY=

# RabbitMQ
RABBITMQ_HOST=
RABBITMQ_PORT=
RABBITMQ_EXCHANGE_NAME=
RABBITMQ_EXCHANGE_TYPE=
RABBITMQ_AUTOPOST_ARTICLE_GEN_QUEUE=
RABBITMQ_AUTOPOST_ROUTING_KEY=

# AWS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# Stripe
STRIPE_API_KEY=
STRIPE_ENDPOINT_SECRET=
STRIPE_CUSTOMER_PORTAL_LINK=
STRIPE_CUSTOMER_PORTAL_RETURN_URL=

# Cloudflare
CLOUDFLARE_ACCOUNT_ID= # account id
CLOUDFLARE_R2_ACCESS_KEY= # api key
CLOUDFLARE_R2_SECRET_KEY= # api secret key
CLOUDFLARE_R2_BUCKET_NAME= # bucket name
CLOUDFLARE_R2_DOMAIN= # domain name
CLOUDFLARE_R2_ENV= # environment name

# Celery
CELERY_TIMEZONE=UTC
CELERY_BROKER_URL= # your redis url eg: redis://localhost:6378/2
CELERY_RESULT_BACKEND= # your redis url eg:redis://localhost:6378/2
CELERY_WORKER_CONCURRENCY= # number of workers to run. eg 16
CELERY_WORKER_MAX_MEMORY_PER_CHILD= # max memory per worker in MB. eg 100000

# DEEP Infra API Token for AI image generation
DEEPINFRA_API_TOKEN= # auth token for deep infra api

# DATAFORSEO
DATAFORSEO_LOGIN= # YOUR email
DATAFORSEO_PASSWORD= # YOUR password

# KEYWORDSEVERYWHERE
KEYWORDSEVERYWHERE_API_KEY= # api key

# SEGMIND API
SEGMIND_API_KEY= # api key

# GOOGLE SERPER API
GOOGLE_SERPER_API_KEY= # api key

# ADMIN DOMAIN (used in failed article email notification)
ADMIN_DOMAIN=http://localhost:3001

# Google reCAPTCHA
RECAPTCHA_SECRET_KEY=6Lcy5WoqAAAAAEIChTjpBF-xQATvKkeu8fjGVHfo

# FLY.io (Article Generation)
FLY_ARTICLE_GEN_DEPLOY_TOKEN=not-required-for-development
FLY_ARTICLE_GEN_APP_NAME=not-required-for-development

# FLY.io (Website Scanning)
FLY_WEBSITE_SCANNING_DEPLOY_TOKEN=not-required-for-development
FLY_WEBSITE_SCANNING_APP_NAME=not-required-for-development

# FLY.io (Competitor Finder)
FLY_COMPETITOR_FINDER_DEPLOY_TOKEN=not-required-for-development
FLY_COMPETITOR_FINDER_APP_NAME=not-required-for-development

# FLY.io (Article Internal Linking)
FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN=not-required-for-development
FLY_ARTICLE_INTERNAL_LINK_APP_NAME=not-required-for-development

# FLY.io (AI Calculator)
FLY_AI_CALCULATOR_DEPLOY_TOKEN=not-required-for-development
FLY_AI_CALCULATOR_APP_NAME=not-required-for-development

REDDIT_SECRET_KEY= # api key
REDDIT_CLIENT_KEY= # api key
