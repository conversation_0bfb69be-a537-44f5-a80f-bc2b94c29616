#!/bin/zsh

read -r "VERSION?Enter Version Number (ex. v1.0.0): "
printf "\n"

IMAGE_URL=registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:"$VERSION"

printf "Proceeding to build and push image %s\n" "$IMAGE_URL"

# ------ remove existing image - if any
docker image rm "$IMAGE_URL"

# ------ build the image
docker buildx build --platform linux/amd64 -f K8/Dockerfile -t "$IMAGE_URL" .

# ------ push the image
docker push "$IMAGE_URL"
