# ===================================================================================================
# ######################################### NOT IN USE CURRENTLY ####################################
# ===================================================================================================
from typing import Dict


def template_feature_image(photo_url: str, logo_url: str, article_title: str,
                           article_uid: str, template_id: str, template_text: bool) -> Dict:
    """
    Generates Bannerbear template data for the provided "template_id".

    :param photo_url: Background image on right.
    :param logo_url: Logo on top left.
    :param article_title: main text.
    :param article_uid: for storing in metadata.
    :param template_id: template id (ex. yKBqAzZ9xwB0bvMx36).
    """
    return {
        "template": template_id,
        "modifications": [
            {
                "name": "photo",
                "image_url": photo_url
            },
            {
                "name": "overlay",
                "color": None
            },
            {
                "name": "logo",
                "image_url": logo_url
            },
            {
                "name": "title",
                "text": article_title if template_text else "\u200B",
                "color": None,
                "background": None
            }
        ],
        "transparent": False,
        "metadata": article_uid,
    }


def template_performance_chart(logo_url: str, website_domain: str) -> Dict:
    """
    Generates Bannerbear template data for template "6jq8BX57eWNKbnlwWa".

    :param logo_url: Logo on top right.
    :param website_domain: Website mode domain value (metadata).
    """
    return {
        "template": "6jq8BX57eWNKbnlwWa",
        "modifications": [
            {
                "name": "user_logo",
                "image_url": logo_url
            },
        ],
        "transparent": False,
        "metadata": website_domain,
    }
