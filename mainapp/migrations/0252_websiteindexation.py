# Generated by Django 5.0 on 2025-06-18 11:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0251_website_toggle_bullet_points_website_toggle_faq_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebsiteIndexation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('search_engine', models.CharField(choices=[('google', 'Google')], max_length=50)),
                ('indexation_details', models.JSONField(default=None, null=True)),
                ('completed', models.BooleanField(default=False)),
                ('urls_sent_for_indexing', models.IntegerField(default=0)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('website', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
