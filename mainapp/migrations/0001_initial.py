# Generated by Django 4.1.7 on 2023-07-20 15:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import mainapp.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('email', models.EmailField(db_index=True, max_length=254, unique=True)),
                ('username', models.CharField(max_length=150)),
                ('user_timezone', models.CharField(default='UTC', max_length=100)),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plugin_uid', models.CharField(default=mainapp.models.generate_plugin_uid, max_length=40)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('article_uid', models.CharField(db_index=True, max_length=300, unique=True)),
                ('title', models.TextField()),
                ('url', models.TextField(default=None, null=True)),
                ('content', models.TextField(default=None, null=True)),
                ('featured_image_url', models.TextField(default=None, null=True)),
                ('word_count', models.PositiveIntegerField(default=None, null=True)),
                ('image_count', models.PositiveIntegerField(default=None, null=True)),
                ('internal_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('external_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('is_processing', models.BooleanField(default=False)),
                ('is_generated', models.BooleanField(default=False)),
                ('is_posted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Competitor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=253)),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('protocol', models.CharField(choices=[('http', 'http'), ('https', 'https')], default='http', max_length=10)),
                ('logo_url', models.CharField(max_length=1000)),
                ('user_generated', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Keyword',
            fields=[
                ('keyword_md5_hash', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('user_generated', models.BooleanField(default=False)),
                ('keyword', models.CharField(max_length=300)),
                ('source', models.CharField(max_length=100)),
                ('country', models.CharField(max_length=100)),
                ('serp_position', models.PositiveIntegerField()),
                ('volume', models.PositiveIntegerField()),
                ('cpc_currency', models.CharField(max_length=5)),
                ('cpc_value', models.DecimalField(decimal_places=2, max_digits=6)),
                ('paid_difficulty', models.DecimalField(decimal_places=2, max_digits=4)),
                ('trend', models.JSONField(default=mainapp.models.keywords_trend_default)),
            ],
        ),
        migrations.CreateModel(
            name='ProTip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('team_uid', models.CharField(db_index=True, default=mainapp.models.generate_plugin_uid, max_length=30, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Website',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_plan_generation_done', models.BooleanField(default=False)),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('protocol', models.CharField(choices=[('http', 'http'), ('https', 'https')], default='http', max_length=10)),
                ('wp_site_url', models.CharField(blank=True, default='', max_length=255)),
                ('title', models.CharField(blank=True, default='', max_length=100)),
                ('description', models.TextField(blank=True, default='')),
                ('logo_url', models.CharField(blank=True, default='', max_length=100)),
                ('logo_dominant_color', models.CharField(default='#000000', max_length=10)),
                ('icp_text', models.TextField(blank=True, default='')),
                ('industry', models.CharField(blank=True, default='', max_length=250)),
                ('title_gen_using_alt_set', models.BooleanField(default=False)),
                ('keyword_strategy', models.CharField(choices=[('volume', 'Volume Based'), ('cpc', 'CPC Based'), ('competition', 'Competition Based')], default='cpc', max_length=50)),
                ('competitors', models.ManyToManyField(to='mainapp.competitor')),
                ('keywords', models.ManyToManyField(to='mainapp.keyword')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TitleGenKeywordsAlt',
            fields=[
                ('uid', models.CharField(db_index=True, max_length=300, primary_key=True, serialize=False)),
                ('used', models.BooleanField(default=False)),
                ('keyword', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.keyword')),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
        migrations.CreateModel(
            name='TitleGenKeywords',
            fields=[
                ('uid', models.CharField(db_index=True, max_length=300, primary_key=True, serialize=False)),
                ('used', models.BooleanField(default=False)),
                ('keyword', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.keyword')),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('is_owner', models.BooleanField(default=False, help_text='This will override all the individual permissions')),
                ('invite_members', models.BooleanField(default=False, help_text='Also handles cancel invitation permission', verbose_name='Invite Members')),
                ('remove_members', models.BooleanField(default=False, verbose_name='Remove Members')),
                ('change_member_permissions', models.BooleanField(default=False, verbose_name='Change Member Permissions')),
                ('change_team_name', models.BooleanField(default=False, verbose_name='Change Team Name')),
                ('change_billing_details', models.BooleanField(default=False, help_text='Credit card & billing details', verbose_name='Change Billing Details')),
                ('create_subscription', models.BooleanField(default=False, help_text='For both purchasing new plan & changing current plan to a new (paid) one', verbose_name='Create Subscription')),
                ('cancel_subscription', models.BooleanField(default=False, help_text='For both cancel button and downgrading to free plan', verbose_name='Cancel Subscription')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.team')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='KubernetesJob',
            fields=[
                ('job_id', models.CharField(db_index=True, max_length=100, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('running', 'running'), ('completed', 'completed'), ('failed', 'failed')], max_length=20)),
                ('fail_reason', models.TextField(blank=True, default='')),
                ('retry_attempts', models.PositiveSmallIntegerField(default=0)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
        migrations.AddIndex(
            model_name='keyword',
            index=models.Index(fields=['keyword_md5_hash'], name='mainapp_key_keyword_a896f1_idx'),
        ),
        migrations.AddIndex(
            model_name='keyword',
            index=models.Index(fields=['keyword'], name='mainapp_key_keyword_df53b9_idx'),
        ),
        migrations.AddIndex(
            model_name='keyword',
            index=models.Index(fields=['keyword', 'source'], name='mainapp_key_keyword_c6f73d_idx'),
        ),
        migrations.AddIndex(
            model_name='keyword',
            index=models.Index(fields=['keyword', 'country'], name='mainapp_key_keyword_ea2bae_idx'),
        ),
        migrations.AddIndex(
            model_name='keyword',
            index=models.Index(fields=['keyword', 'source', 'country'], name='mainapp_key_keyword_40ac2e_idx'),
        ),
        migrations.AddField(
            model_name='competitor',
            name='keywords',
            field=models.ManyToManyField(to='mainapp.keyword'),
        ),
        migrations.AddField(
            model_name='article',
            name='keyword',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.keyword'),
        ),
        migrations.AddField(
            model_name='article',
            name='website',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='user',
            name='current_active_team',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='mainapp.team'),
        ),
        migrations.AddField(
            model_name='user',
            name='current_active_website',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='active_website', to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='user',
            name='team',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='owner', to='mainapp.team'),
        ),
    ]
