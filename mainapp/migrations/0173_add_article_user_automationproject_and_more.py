# Generated by Django 5.0 on 2024-12-03 13:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0172_migrate_users_to_website_model'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='article',
            name='user',
        ),
        migrations.RemoveField(
            model_name='automationproject',
            name='user',
        ),
        migrations.RemoveField(
            model_name='keyword',
            name='user',
        ),
        migrations.RemoveField(
            model_name='keywordproject',
            name='user',
        ),
        migrations.RemoveField(
            model_name='programmaticseotitle',
            name='user',
        ),
        migrations.RemoveField(
            model_name='user',
            name='ai_generated_image_style',
        ),
        migrations.RemoveField(
            model_name='user',
            name='article_language_preference',
        ),
        migrations.RemoveField(
            model_name='user',
            name='article_tone_of_voice',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auto_publish',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auto_publish_article_count',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auto_publish_days',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auto_publish_email_hours',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auto_publish_time',
        ),
        migrations.RemoveField(
            model_name='user',
            name='external_backlinks_preference',
        ),
        migrations.RemoveField(
            model_name='user',
            name='feature_image_required',
        ),
        migrations.RemoveField(
            model_name='user',
            name='feature_image_template_id',
        ),
        migrations.RemoveField(
            model_name='user',
            name='generate_bannerbear_featured_image',
        ),
        migrations.RemoveField(
            model_name='user',
            name='image_source',
        ),
        migrations.RemoveField(
            model_name='user',
            name='images_file_format',
        ),
        migrations.RemoveField(
            model_name='user',
            name='keyword_strategy',
        ),
        migrations.RemoveField(
            model_name='user',
            name='max_external_backlinks',
        ),
        migrations.RemoveField(
            model_name='user',
            name='max_internal_backlinks',
        ),
        migrations.RemoveField(
            model_name='website',
            name='keywords',
        ),
        migrations.RemoveField(
            model_name='website',
            name='listicle_keywords',
        ),
        migrations.RemoveField(
            model_name='website',
            name='selected_keywords',
        ),
        migrations.RemoveField(
            model_name='website',
            name='user_added_keywords',
        ),
         migrations.RemoveField(
            model_name='user',
            name='shopify_integrations',
        ),
        migrations.RemoveField(
            model_name='user',
            name='webflow_integrations',
        ),
        migrations.RemoveField(
            model_name='user',
            name='wix_integrations',
        ),
        migrations.RemoveField(
            model_name='user',
            name='wordpress_integrations',
        ),
    ]