# Generated by Django 5.0 on 2025-02-19 08:34

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0207_merge_0200_changelog_0206_gsckeywordstatus_created_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='is_failed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='website',
            name='task_queued',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='WebsiteScanQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sitemap_urls', django.contrib.postgres.fields.ArrayField(base_field=models.URLField(), default=list, null=True, size=None)),
                ('website_urls', django.contrib.postgres.fields.ArrayField(base_field=models.URLField(), default=list, null=True, size=None)),
                ('status', models.CharField(choices=[('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='queued', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
