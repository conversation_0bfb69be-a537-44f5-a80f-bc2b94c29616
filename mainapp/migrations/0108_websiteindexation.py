# Generated by Django 5.0 on 2024-04-10 09:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0107_blockwebsitekeywords'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebsiteIndexation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('search_engine', models.CharField(choices=[('google', 'Google')], max_length=50)),
                ('indexation_details', models.J<PERSON>NField(default=None, null=True)),
                ('completed', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('website', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
