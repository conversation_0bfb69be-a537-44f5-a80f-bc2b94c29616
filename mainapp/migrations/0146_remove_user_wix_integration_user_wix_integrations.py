# Generated by Django 5.0 on 2024-09-17 14:29

from django.db import migrations, models, transaction
from django.db.migrations.state import StateApps

from mainapp.models import User


def migrate_users_wix_integration(apps: StateApps, _):
    """
    Migrate users wix integration to new field
    :param apps: Django StateApps
    """
    with transaction.atomic():
        user_model: User = apps.get_model('mainapp', "User")
        users: models.QuerySet[User] = user_model.objects.filter(wix_integration__isnull=False)

        for user in users.all():
            user.wix_integrations.add(user.wix_integration)


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0145_remove_user_wordpress_integration_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='wix_integrations',
            field=models.ManyToManyField(to='mainapp.wixintegration'),
        ),

        # Migrate user wix integration to new field
        migrations.RunPython(migrate_users_wix_integration),

        migrations.RemoveField(
            model_name='user',
            name='wix_integration',
        ),
    ]
