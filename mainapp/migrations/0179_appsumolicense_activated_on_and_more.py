# Generated by Django 5.0 on 2024-12-18 08:26

import datetime
import django.utils.timezone
from django.db import migrations, models
from dateutil.relativedelta import relativedelta

def set_next_renewal_date(apps, schema_editor):
    AppSumoLicense = apps.get_model('mainapp', 'AppSumoLicense')
    for license in AppSumoLicense.objects.all():
        license.next_renewal_date = license.created_on + relativedelta(days=30)
        license.save()

class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0178_merge_20241213_0838'),
    ]

    operations = [
        migrations.AddField(
            model_name='appsumolicense',
            name='activated_on',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='appsumolicense',
            name='deactivated_on',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='appsumolicense',
            name='next_renewal_date',
            field=models.DateTimeField(null=True, default=None),
            preserve_default=False,
        ),
        migrations.RunPython(set_next_renewal_date),
    ]
