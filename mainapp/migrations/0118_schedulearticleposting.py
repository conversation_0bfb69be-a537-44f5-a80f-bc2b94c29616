# Generated by Django 5.0 on 2024-07-10 12:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0117_alter_article_user_alter_autopublish_user_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScheduleArticlePosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schedule_datetime', models.DateTimeField()),
                ('schedule_on', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.article')),
            ],
        ),
    ]
