# Generated by Django 5.0 on 2024-04-22 11:41

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0108_article_article_description_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='ListicleKeywords',
            field=models.ManyToManyField(db_index=True, related_name='listicle_keywords', to='mainapp.keyword'),
        ),
        migrations.CreateModel(
            name='Listicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('article_uid', models.CharField(db_index=True, max_length=300, unique=True)),
                ('title', models.TextField()),
                ('url', models.TextField(default=None, null=True)),
                ('content', models.TextField(default=None, null=True)),
                ('article_description', models.TextField(default=None, null=True)),
                ('word_count', models.PositiveIntegerField(default=None, null=True)),
                ('image_count', models.PositiveIntegerField(default=None, null=True)),
                ('internal_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('external_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('article_link', models.TextField(default=None, null=True)),
                ('is_processing', models.BooleanField(default=False)),
                ('is_failed', models.BooleanField(default=False)),
                ('is_generated', models.BooleanField(default=False)),
                ('is_posted', models.BooleanField(default=False)),
                ('is_archived', models.BooleanField(default=False)),
                ('posted_to', models.CharField(choices=[('wordpress', 'wordpress'), ('webflow', 'webflow')], default='wordpress', max_length=20)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('article_images', models.ManyToManyField(to='mainapp.articleimage')),
                ('featured_images', models.ManyToManyField(related_name='listicle_featured_images', to='mainapp.featuredimage')),
                ('keyword', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.keyword')),
                ('selected_featured_image', models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.featuredimage')),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
