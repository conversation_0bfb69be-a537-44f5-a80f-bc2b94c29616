# Generated by Django 5.0 on 2024-09-16 08:34

from django.db import migrations, models, transaction
from django.db.migrations.state import StateApps

from mainapp.models import User


def migrate_users_wordpress_integration(apps: StateApps, _):
    """
    Migrate users wordpress integration to new field
    :param apps: Django StateApps
    """
    with transaction.atomic():
        user_model: User = apps.get_model('mainapp', "User")
        users: models.QuerySet[User] = user_model.objects.filter(wordpress_integration__isnull=False)

        for user in users.all():
            user.wordpress_integrations.add(user.wordpress_integration)

class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0144_rename_selected_integration_automationproject_selected_integration_name_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='wordpress_integrations',
            field=models.ManyToManyField(to='mainapp.wordpressintegration'),
        ),

        # Migrate user wordpress integration to new field
        migrations.RunPython(migrate_users_wordpress_integration),

        migrations.RemoveField(
            model_name='user',
            name='wordpress_integration',
        ),
    ]
