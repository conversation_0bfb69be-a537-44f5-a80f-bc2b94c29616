# Generated by Django 5.0 on 2024-03-07 07:29

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0090_website_ai_generated_image_style_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIArticleImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image_url', models.TextField()),
                ('context_title', models.TextField()),
                ('generated_context_description', models.TextField()),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
    ]
