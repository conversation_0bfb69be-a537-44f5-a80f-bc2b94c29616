# Generated by Django 5.0 on 2024-07-11 22:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0123_automationproject'),
    ]

    operations = [
        migrations.AddField(
            model_name='article',
            name='associated_automation_project',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.automationproject'),
        ),
        migrations.AddField(
            model_name='automationproject',
            name='last_ran_on',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='automationproject',
            name='published_articles_count',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
