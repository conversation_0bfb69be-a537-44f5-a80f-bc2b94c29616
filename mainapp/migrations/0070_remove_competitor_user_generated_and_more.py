# Generated by Django 5.0 on 2024-01-04 07:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0069_alter_hypestatdata_domain_authority_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='competitor',
            name='user_generated',
        ),
        migrations.AddField(
            model_name='competitor',
            name='keywords_generated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='competitor',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AlterField(
            model_name='competitor',
            name='domain',
            field=models.CharField(db_index=True, max_length=253),
        ),
        migrations.AlterField(
            model_name='website',
            name='competitors',
            field=models.ManyToManyField(db_index=True, related_name='websites_deprecated', to='mainapp.competitor'),
        ),
    ]
