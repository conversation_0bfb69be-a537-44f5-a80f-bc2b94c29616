# Generated by Django 4.1.7 on 2023-12-21 13:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0067_merge_20231220_1129'),
    ]

    operations = [
        migrations.CreateModel(
            name='HypestatData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('organic_traffic', models.IntegerField(null=True)),
                ('organic_keywords', models.IntegerField(null=True)),
                ('domain_authority', models.IntegerField(null=True)),
                ('total_backlinks', models.IntegerField(null=True)),
                ('follow', models.IntegerField(null=True)),
                ('no_follow', models.IntegerField(null=True)),
                ('referring_domains', models.Integer<PERSON>ield(null=True)),
            ],
        ),
    ]
