# Generated by Django 5.0 on 2025-01-03 15:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0182_blogfinder'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='blogfinder',
            name='id',
        ),
        migrations.AddField(
            model_name='blogfinder',
            name='blog_md5_hash',
            field=models.CharField(default='', max_length=50, primary_key=True, serialize=False),
        ),
        migrations.CreateModel(
            name='BlogFinderProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('blog_project_id', models.CharField(db_index=True, max_length=300, unique=True)),
                ('blog_project_name', models.CharField(max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('blog_finder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blog_finder_project', to='mainapp.blogfinder')),
                ('website', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='blog_finder_project', to='mainapp.website')),
            ],
        ),
    ]
