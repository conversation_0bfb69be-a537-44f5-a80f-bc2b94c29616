# Generated by Django 4.1.7 on 2023-10-14 08:16

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0039_alter_website_image_source'),
    ]

    operations = [
        migrations.CreateModel(
            name='OpenaiTimeoutLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incident_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('timeout_value', models.PositiveIntegerField()),
                ('source', models.CharField(choices=[('icp', 'ICP/Industry'), ('competitors', 'Competitors'), ('article_generation', 'Article Generation'), ('title_generation', 'Title Generation')], max_length=100)),
            ],
        ),
    ]
