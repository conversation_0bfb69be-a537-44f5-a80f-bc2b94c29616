# Generated by Django 5.0 on 2024-03-04 10:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0089_merge_0086_blockdomain_0088_merge_20240301_0945'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='website',
            name='ai_generated_image_style',
            field=models.CharField(default='illustration', max_length=50),
        ),
        migrations.AlterField(
            model_name='featuredimage',
            name='source',
            field=models.CharField(choices=[('defaultimage', 'Default Image'), ('bannerbear', 'Bannerbear'), ('ai_image_generation', 'AI Image Generation')], max_length=300),
        ),
        migrations.AlterField(
            model_name='website',
            name='feature_image_template_id',
            field=models.CharField(default='AI-Generated-Image', max_length=30),
        ),
        migrations.AlterField(
            model_name='website',
            name='generate_bannerbear_featured_image',
            field=models.<PERSON><PERSON><PERSON><PERSON>ield(default=True),
        ),
        migrations.AlterField(
            model_name='website',
            name='image_source',
            field=models.CharField(choices=[('no_image', 'No Image'), ('unsplash', 'Unsplash'), ('google', 'Google Images'), ('ai_image_generation', 'AI Image Generation')], default='ai_image_generation', max_length=100),
        ),
    ]
