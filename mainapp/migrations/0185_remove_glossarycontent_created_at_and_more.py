# Generated by Django 5.0 on 2024-12-30 12:30

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0184_glossarycontent'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='glossarycontent',
            name='created_at',
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='created_on',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='feedback',
            field=models.CharField(choices=[('positive', 'Positive'), ('negative', 'Negative'), ('no_feedback', 'No Feedback')], default='no_feedback', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='generated_on',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='glossary_link',
            field=models.TextField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='internal_link_count',
            field=models.PositiveIntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='is_archived',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='is_failed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='is_generated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='is_posted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='is_processing',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='posted_on',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='posted_to',
            field=models.CharField(choices=[('wordpress', 'wordpress'), ('webflow', 'webflow'), ('wix', 'wix'), ('shopify', 'shopify')], default='wordpress', max_length=20),
        ),
        migrations.AddField(
            model_name='glossarycontent',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
    ]
