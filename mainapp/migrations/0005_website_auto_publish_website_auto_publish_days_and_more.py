# Generated by Django 4.1.7 on 2023-08-02 11:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0004_websiteintegration_website_integration'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='auto_publish',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='website',
            name='auto_publish_days',
            field=models.CharField(choices=[('all_days', 'All Days'), ('weekdays', 'Weekdays (Monday to Friday)'), ('weekends', 'Weekends (Saturday & Sunday)'), ('alternate_days', 'Alternate Days')], default='all_days', max_length=300),
        ),
        migrations.AddField(
            model_name='website',
            name='auto_publish_email_hours',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='website',
            name='auto_publish_time',
            field=models.CharField(choices=[('morning', 'Morning (06:00 AM to 12:00 PM)'), ('afternoon', 'Afternoon (12:00 PM to 06:00 PM)'), ('evening', 'Evening (06:00 PM to 12:00 AM)'), ('night', 'Night (12:00 AM to 06:00 AM)')], default='morning', max_length=300),
        ),
        migrations.AddField(
            model_name='website',
            name='image_source',
            field=models.CharField(choices=[('unsplash', 'Unsplash'), ('google_images', 'Google Images')], default='google_images', max_length=100),
        ),
    ]
