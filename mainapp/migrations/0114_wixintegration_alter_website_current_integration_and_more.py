# Generated by Django 5.0 on 2024-05-24 10:03

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0113_randomlyscheduledarticles'),
    ]

    operations = [
        migrations.CreateModel(
            name='WixIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=1000)),
                ('site_id', models.CharField(max_length=300)),
                ('site_url', models.TextField()),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.AlterField(
            model_name='website',
            name='current_integration',
            field=models.CharField(choices=[('wordpress', 'Wordpress'), ('webflow', 'Webflow'), ('wix', 'WIX')], default=None, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='wix_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.wixintegration'),
        ),
    ]
