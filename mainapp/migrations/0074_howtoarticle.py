# Generated by Django 5.0 on 2024-01-29 17:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0073_alter_website_article_tone_of_voice'),
    ]

    operations = [
        migrations.CreateModel(
            name='HowToArticle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('article_uid', models.CharField(db_index=True, max_length=300, unique=True)),
                ('title', models.TextField()),
                ('keyword', models.CharField(default=None, max_length=300, null=True)),
                ('url', models.TextField(default=None, null=True)),
                ('content', models.TextField(default=None, null=True)),
                ('word_count', models.PositiveIntegerField(default=None, null=True)),
                ('image_count', models.PositiveIntegerField(default=None, null=True)),
                ('internal_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('external_link_count', models.PositiveIntegerField(default=None, null=True)),
                ('article_link', models.TextField(default=None, null=True)),
                ('is_processing', models.BooleanField(default=False)),
                ('is_failed', models.BooleanField(default=False)),
                ('is_generated', models.BooleanField(default=False)),
                ('is_posted', models.BooleanField(default=False)),
                ('is_archived', models.BooleanField(default=False)),
                ('featured_image', models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.featuredimage')),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
