# Generated by Django 5.0 on 2024-12-19 11:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0178_merge_20241213_0838'),
    ]

    operations = [
        migrations.CreateModel(
            name='GlossaryTopic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('word', models.TextField(blank=True, default='')),
                ('keyword_project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='glossary_topics', to='mainapp.keywordproject')),
            ],
        ),
    ]
