# Generated by Django 5.0 on 2025-02-14 14:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0204_gsckeywordstatus_created_at'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='gsckeywordstatus',
            name='created_at',
        ),
        migrations.AddField(
            model_name='gsckeywordstatus',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
    ]
