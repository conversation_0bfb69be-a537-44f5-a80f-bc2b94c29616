# Generated by Django 5.0 on 2024-03-11 09:01

import django.contrib.postgres.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0092_remove_website_google_search_console_integrated_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GoogleIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('integration_type', models.CharField(choices=[('google-search-console', 'Google Search Console'), ('google-analytics', 'Google Analytics'), ('google-drive', 'Google Drive')], max_length=50)),
                ('token', models.CharField(max_length=300)),
                ('refresh_token', models.CharField(max_length=150)),
                ('token_uri', models.Char<PERSON>ield(max_length=50)),
                ('client_id', models.Char<PERSON>ield(max_length=100)),
                ('client_secret', models.Char<PERSON>ield(max_length=50)),
                ('scopes', django.contrib.postgres.fields.ArrayField(base_field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=150), size=None), size=None)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
