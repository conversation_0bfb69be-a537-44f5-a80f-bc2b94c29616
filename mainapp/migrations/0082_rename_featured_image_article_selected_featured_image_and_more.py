# Generated by Django 5.0 on 2024-02-16 09:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0081_articleimage_article_article_images_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='article',
            old_name='featured_image',
            new_name='selected_featured_image',
        ),
        migrations.RenameField(
            model_name='howtoarticle',
            old_name='featured_image',
            new_name='selected_featured_image',
        ),
        migrations.AddField(
            model_name='article',
            name='featured_images',
            field=models.ManyToManyField(related_name='general_article_featured_images', to='mainapp.featuredimage'),
        ),
        migrations.AddField(
            model_name='featuredimage',
            name='template_id_used',
            field=models.CharField(default='', max_length=30),
        ),
        migrations.AddField(
            model_name='featuredimage',
            name='template_image_url',
            field=models.TextField(default=''),
        ),
        migrations.Add<PERSON>ield(
            model_name='howtoarticle',
            name='featured_images',
            field=models.ManyToManyField(related_name='how_to_article_featured_images', to='mainapp.featuredimage'),
        ),
    ]
