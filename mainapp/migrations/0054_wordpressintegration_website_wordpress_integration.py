# Generated by Django 4.1.7 on 2023-11-17 07:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0053_website_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='WordpressIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('wp_admin_url', models.TextField()),
                ('auth_url', models.TextField()),
                ('site_url', models.TextField()),
                ('user_login', models.CharField(max_length=300)),
                ('password', models.<PERSON>r<PERSON>ield(max_length=300)),
            ],
        ),
        migrations.AddField(
            model_name='website',
            name='wordpress_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.wordpressintegration'),
        ),
    ]
