# Generated by Django 4.1.7 on 2023-12-02 10:37

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0064_website_content_plan_task_data'),
    ]

    operations = [
        migrations.CreateModel(
            name='KubernetesJobLogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('message', models.TextField()),
                ('type', models.CharField(choices=[('info', 'Info'), ('warning', 'Warning'), ('error', 'Error')], max_length=100)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.kubernetesjob')),
            ],
        ),
    ]
