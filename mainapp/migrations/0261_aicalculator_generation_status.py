# Generated by Django 5.0 on 2025-07-05 12:48

from django.db import migrations, models


def update_existing_calculators_status(apps, schema_editor):
    """
    Update all existing AICalculator instances to have generation_status='completed'
    since they were already generated before this field was added.
    """
    AICalculator = apps.get_model('mainapp', 'AICalculator')
    AICalculator.objects.all().update(generation_status='completed')


def reverse_update_existing_calculators_status(apps, schema_editor):
    """
    Reverse migration: set all calculators back to 'processing' status.
    """
    AICalculator = apps.get_model('mainapp', 'AICalculator')
    AICalculator.objects.all().update(generation_status='processing')


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0260_webpage_schema_enabled'),
    ]

    operations = [
        migrations.AddField(
            model_name='aicalculator',
            name='generation_status',
            field=models.CharField(choices=[('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('modifying', 'Modifying')], default='processing', max_length=20),
        ),
        migrations.RunPython(
            update_existing_calculators_status,
            reverse_update_existing_calculators_status,
        ),
    ]
