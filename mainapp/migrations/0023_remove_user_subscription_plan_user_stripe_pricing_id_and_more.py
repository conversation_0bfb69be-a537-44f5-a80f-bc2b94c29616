# Generated by Django 4.1.7 on 2023-08-30 07:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0022_alter_subscriptionplan_stripe_subscription_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='subscription_plan',
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_pricing_id',
            field=models.CharField(default=None, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_product_id',
            field=models.CharField(default=None, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_subscription_id',
            field=models.CharField(default=None, max_length=300, null=True),
        ),
        migrations.DeleteModel(
            name='SubscriptionPlan',
        ),
    ]
