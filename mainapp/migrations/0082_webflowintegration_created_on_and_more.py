# Generated by Django 5.0 on 2024-02-09 11:40

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0081_website_webflow_integration_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='webflowintegration',
            name='created_on',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='webflowintegration',
            name='site_url',
            field=models.TextField(default=django.utils.timezone.now),
            preserve_default=False,
        ),
    ]
