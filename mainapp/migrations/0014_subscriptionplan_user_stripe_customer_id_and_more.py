# Generated by Django 4.1.7 on 2023-08-22 13:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0013_remove_website_wp_site_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_product_id', models.CharField(max_length=300)),
                ('stripe_pricing_id', models.CharField(max_length=300)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_customer_id',
            field=models.Char<PERSON>ield(default=None, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='subscription_plan',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.subscriptionplan'),
        ),
    ]
