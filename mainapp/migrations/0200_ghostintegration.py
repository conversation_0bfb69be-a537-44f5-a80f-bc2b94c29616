# Generated by Django 5.0 on 2025-02-14 14:45

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0199_user_last_rpf_queries_reset_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GhostIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_url', models.TextField()),
                ('api_key', models.CharField(max_length=1000)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('website', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
