# Generated by Django 5.0 on 2025-01-02 11:59

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0181_appsumolicense_reason'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogFinder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('blog_url', models.TextField(default=None, null=True)),
                ('isvalid', models.BooleanField(default=False)),
                ('author_name', models.CharField(max_length=300)),
                ('email_address', models.Char<PERSON>ield(max_length=300)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('website', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='blog_finder', to='mainapp.website')),
            ],
        ),
    ]
