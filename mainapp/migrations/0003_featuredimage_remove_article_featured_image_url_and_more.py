# Generated by Django 4.1.7 on 2023-07-22 11:46

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0002_alter_article_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeaturedImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='featuredimages')),
                ('source', models.CharField(choices=[('bannerbear', 'Bannerbear')], max_length=300)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.RemoveField(
            model_name='article',
            name='featured_image_url',
        ),
        migrations.AddField(
            model_name='article',
            name='featured_image',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.featuredimage'),
        ),
    ]
