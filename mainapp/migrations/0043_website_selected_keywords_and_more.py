# Generated by Django 4.1.7 on 2023-10-28 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0042_website_top_keywords_by_volume'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='selected_keywords',
            field=models.ManyToManyField(related_name='selected_by_website', to='mainapp.keyword'),
        ),
        migrations.AddField(
            model_name='website',
            name='user_added_keywords',
            field=models.ManyToManyField(related_name='user_added', to='mainapp.keyword'),
        ),
    ]
