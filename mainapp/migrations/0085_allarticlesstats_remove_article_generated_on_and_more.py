# Generated by Django 5.0 on 2024-02-23 09:53

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0084_merge_20240221_1530'),
    ]

    operations = [
        migrations.CreateModel(
            name='AllArticlesStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('generated_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('is_successful', models.BooleanField(default=False)),
                ('article_uid', models.CharField(db_index=True, max_length=300, unique=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='article',
            name='generated_on',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='generated_on',
        ),
    ]
