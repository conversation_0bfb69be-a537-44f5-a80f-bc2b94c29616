# Custom migration script created on 2024-07-10 12:00

import uuid

from django.db import migrations, transaction
from django.db.models import Sum, F, Q, Subquery, OuterRef
from django.db.models.functions import Coalesce
from django.db.migrations.state import StateApps

from mainapp.models import (KeywordProject, Website, Article, GPT4UsageStats,
                            KubernetesJob, Keyword, Competitor)


def migrate_users_and_settings(apps: StateApps, _):
    """
    Migrates the User & Settings to new schema
    :param apps: Django StateApps
    """
    article_model: Article = apps.get_model('mainapp', 'Article')
    gpt4usagestats_model: GPT4UsageStats = apps.get_model('mainapp', 'GPT4UsageStats')
    kubernetesjob_model: KubernetesJob = apps.get_model('mainapp', 'KubernetesJob')
    keyword_model: Keyword = apps.get_model('mainapp', 'Keyword')
    website_model: Website = apps.get_model('mainapp', 'Website')
    keywordproject_model: KeywordProject = apps.get_model('mainapp', 'KeywordProject')
    competitor_model: Competitor = apps.get_model('mainapp', 'Competitor')
    
    models_to_update = [article_model, gpt4usagestats_model, kubernetesjob_model]

    with transaction.atomic():
        # Add User instance
        for model in models_to_update:
            for obj in model.objects.all():
                if obj.website:
                    obj.user = obj.website.user
                    obj.save(update_fields=['user'])

        # Fetch Keywords that are not associated with any model
        keywords_to_remove = keyword_model.objects.filter(
            ~(
                Q(website__isnull=False) |
                Q(competitor__isnull=False) |
                Q(article__isnull=False) |
                Q(howtoarticle__isnull=False) |
                Q(listicle__isnull=False) |
                Q(user_added__isnull=False) |
                Q(selected_by_website__isnull=False) |
                Q(listicle_keywords__isnull=False) |
                Q(top_website_set__isnull=False)
            )
        )
        keyword_md5_hash = keywords_to_remove.values_list('keyword_md5_hash', flat=True)

        # Exclude keywords to remove
        keywords_to_update = keyword_model.objects.exclude(keyword_md5_hash__in=keyword_md5_hash)

        # Annotate with the user from the related models
        keywords_to_update = keywords_to_update.annotate(
            related_user=Subquery(
                keyword_model.objects.filter(pk=OuterRef('pk')).annotate(
                    user_website=Subquery(
                        website_model.objects.filter(keywords=OuterRef('pk')).values('user')[:1]
                    ),
                    user_competitor=Subquery(
                        competitor_model.objects.filter(keywords=OuterRef('pk')).values('website__user')[:1]
                    ),
                    user_article=Subquery(
                        article_model.objects.filter(keyword=OuterRef('pk')).values('website__user')[:1]
                    ),
                    user_user_added=Subquery(
                        website_model.objects.filter(user_added_keywords=OuterRef('pk')).values('user')[:1]
                    ),
                    user_selected_by_website=Subquery(
                        website_model.objects.filter(selected_keywords=OuterRef('pk')).values('user')[:1]
                    ),
                    user_listicle_keywords=Subquery(
                        website_model.objects.filter(ListicleKeywords=OuterRef('pk')).values('user')[:1]
                    ),
                    user_top_website_set=Subquery(
                        website_model.objects.filter(top_keywords_by_volume=OuterRef('pk')).values('user')[:1]
                    )
                ).annotate(
                    final_user=Coalesce(
                        F('user_website'),
                        F('user_competitor'),
                        F('user_article'),
                        F('user_user_added'),
                        F('user_selected_by_website'),
                        F('user_listicle_keywords'),
                        F('user_top_website_set')
                    )
                ).values('final_user')
            )
        )

        # Bulk update keywords with the annotated user
        keywords_to_update.update(user=F('related_user'))

        for website in website_model.objects.all():
            user = website.user
            selected_keywords = website.selected_keywords.all()

            if selected_keywords:
                # Migrate Keywords into KeywordsProject model
                keyword_project = keywordproject_model(
                    user=user,
                    project_name=f"{selected_keywords[0].keyword[:15]} - {website.domain}",
                    project_id=str(uuid.uuid4())[:16],
                    total_traffic_volume=selected_keywords.aggregate(total=Sum('volume'))['total'],
                    location_iso_code='US'
                )
                keyword_project.save()
                keyword_project.keywords.set(selected_keywords)

            if user:
                user.ai_generated_image_style = website.ai_generated_image_style
                user.article_language_preference = website.article_language_preference
                user.article_tone_of_voice = website.article_tone_of_voice
                user.auto_publish = website.auto_publish
                user.auto_publish_article_count = website.auto_publish_article_count
                user.auto_publish_days = website.auto_publish_days
                user.auto_publish_email_hours = website.auto_publish_email_hours
                user.auto_publish_time = website.auto_publish_time
                user.current_integration = website.current_integration
                user.external_backlinks_preference = website.external_backlinks_preference
                user.feature_image_template_id = website.feature_image_template_id
                user.feature_image_text_required = website.feature_image_text_required
                user.generate_bannerbear_featured_image = website.generate_bannerbear_featured_image
                user.google_analytics_integrated = website.google_analytics_integrated
                user.google_drive_integrated = website.google_drive_integrated
                user.image_source = website.image_source
                user.images_file_format = website.images_file_format
                user.keyword_strategy = website.keyword_strategy
                user.max_external_backlinks = website.max_external_backlinks
                user.max_internal_backlinks = website.max_internal_backlinks
                user.save(update_fields=[
                    'ai_generated_image_style',
                    'article_language_preference',
                    'article_tone_of_voice',
                    'auto_publish',
                    'auto_publish_article_count',
                    'auto_publish_days',
                    'auto_publish_email_hours',
                    'auto_publish_time',
                    'current_integration',
                    'external_backlinks_preference',
                    'feature_image_template_id',
                    'feature_image_text_required',
                    'generate_bannerbear_featured_image',
                    'google_analytics_integrated',
                    'google_drive_integrated',
                    'image_source',
                    'images_file_format',
                    'keyword_strategy',
                    'max_external_backlinks',
                    'max_internal_backlinks'
                ])


class Migration(migrations.Migration):
    dependencies = [
        ('mainapp', '0114_add_fields_to_user_model_and_more'),
    ]

    operations = [
        # Migrate website model data to user model
        migrations.RunPython(migrate_users_and_settings),
    ]
