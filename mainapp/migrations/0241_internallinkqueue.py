# Generated by Django 5.0 on 2025-05-22 09:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0240_articlegenerationqueue_regenerate'),
    ]

    operations = [
        migrations.CreateModel(
            name='InternalLinkQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='queued', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.article')),
                ('k8_job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.kubernetesjob')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
