# Generated by Django 5.0 on 2024-12-30 11:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0181_appsumolicense_reason'),
    ]

    operations = [
        migrations.CreateModel(
            name='GuestPostFinderQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=250)),
                ('limit', models.IntegerField()),
                ('is_processing', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GuestPostFinderResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('post_title', models.CharField(blank=True, max_length=500)),
                ('post_link', models.CharField(blank=True, max_length=500)),
                ('guest_post_finder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.guestpostfinderquery')),
                ('hypestat', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.hypestatdata')),
            ],
        ),
    ]
