# Generated by Django 5.0 on 2024-12-21 15:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0183_website_last_crawled_website_sitemap_url'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='website',
            name='last_crawled',
        ),
        migrations.AddField(
            model_name='website',
            name='crawling_ends_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='crawling_started_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='is_crawled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='website',
            name='is_crawling',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='WebPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=255, unique=True)),
                ('title', models.CharField(max_length=255)),
                ('summary', models.TextField()),
                ('content', models.TextField()),
                ('embedding_id', models.CharField(max_length=100, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('last_modified_on', models.DateTimeField(blank=True, null=True)),
                ('last_scraped_on', models.DateTimeField()),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
                ('excluded',models.BooleanField(default=False)),
            ],
        ),
    ]
