# Generated by Django 4.1.7 on 2023-11-08 10:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0048_alter_competitor_keywords'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='domain_authority',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='follow',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='no_follow',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='organic_keyword',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='organic_traffic',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='referring_domains',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='total_backlinks',
            field=models.IntegerField(default=None, null=True),
        ),
    ]
