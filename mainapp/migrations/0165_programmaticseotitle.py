# Generated by Django 5.0 on 2024-11-21 08:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0164_alter_article_context'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProgrammaticSeoTitle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword_project', models.CharField(max_length=255)),
                ('titles', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='programmatic_seo_titles', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
