# Generated by Django 5.0 on 2024-07-05 07:24

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0113_user_latest_ip_info'),
    ]

    operations = [
        # Add fields to User model
        migrations.AddField(
            model_name='user',
            name='ai_generated_image_style',
            field=models.CharField(default='illustration', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='article_language_preference',
            field=models.CharField(default='english', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='article_tone_of_voice',
            field=models.CharField(default='exciting', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='auto_publish',
            field=models.CharField(choices=[('on', 'ON'), ('off', 'OFF'), ('paused', 'PAUSED')], default='off', max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='auto_publish_article_count',
            field=models.PositiveIntegerField(default=10),
        ),
        migrations.AddField(
            model_name='user',
            name='auto_publish_days',
            field=models.CharField(choices=[('all_days', 'All Days'), ('weekdays', 'Weekdays (Monday to Friday)'), ('weekends', 'Weekends (Saturday & Sunday)'), ('alternate_days', 'Alternate Days')], default='all_days', max_length=300),
        ),
        migrations.AddField(
            model_name='user',
            name='auto_publish_email_hours',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='user',
            name='auto_publish_time',
            field=models.CharField(choices=[('morning', 'Morning (06:00 AM to 12:00 PM)'), ('afternoon', 'Afternoon (12:00 PM to 06:00 PM)'), ('evening', 'Evening (06:00 PM to 12:00 AM)'), ('night', 'Night (12:00 AM to 06:00 AM)')], default='morning', max_length=300),
        ),
        migrations.AddField(
            model_name='user',
            name='current_integration',
            field=models.CharField(choices=[('wordpress', 'Wordpress'), ('webflow', 'Webflow')], default=None, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='external_backlinks_preference',
            field=models.CharField(default='no-follow', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='feature_image_template_id',
            field=models.CharField(default='AI-Generated-Image', max_length=30),
        ),
        migrations.AddField(
            model_name='user',
            name='feature_image_text_required',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='generate_bannerbear_featured_image',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='google_analytics_integrated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='google_drive_integrated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='image_source',
            field=models.CharField(choices=[('no_image', 'No Image'), ('unsplash', 'Unsplash'), ('google', 'Google Images'), ('ai_image_generation', 'AI Image Generation')], default='ai_image_generation', max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='images_file_format',
            field=models.CharField(choices=[('webp', 'WebP'), ('png', 'PNG'), ('jpeg', 'JPEG')], default='png', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='keyword_strategy',
            field=models.CharField(choices=[('volume', 'Volume Based'), ('cpc', 'CPC Based'), ('competition', 'Competition Based')], default='cpc', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='max_external_backlinks',
            field=models.PositiveIntegerField(default=2),
        ),
        migrations.AddField(
            model_name='user',
            name='max_internal_backlinks',
            field=models.PositiveIntegerField(default=5),
        ),
        migrations.AddField(
            model_name='user',
            name='webflow_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.webflowintegration'),
        ),
        migrations.AddField(
            model_name='user',
            name='wordpress_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.wordpressintegration'),
        ),

        # Create User relation
        migrations.AddField(
            model_name='article',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='autopublish',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='gpt4usagestats',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='howtoarticle',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='keyword',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='listicle',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='titlegenkeywords',
            name='user',
            field=models.ForeignKey(null=True, default=None, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),

        # Create KeywordProject model
        migrations.CreateModel(
            name='KeywordProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_name', models.CharField(max_length=300)),
                ('total_traffic_volume', models.PositiveBigIntegerField(default=0)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('project_id', models.CharField(db_index=True, max_length=300, unique=True)),
                ('location_iso_code', models.CharField(max_length=2)),
                ('keywords', models.ManyToManyField(db_index=True, to='mainapp.keyword')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        )
    ]
