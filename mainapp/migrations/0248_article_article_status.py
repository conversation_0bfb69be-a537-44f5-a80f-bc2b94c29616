# Generated by Django 5.0 on 2025-06-02 14:36

from django.db import migrations, models

def set_article_status(apps, schema_editor):
    Article = apps.get_model('mainapp', 'Article')
    Article.objects.filter(is_posted=True).update(article_status='publish')

class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0247_website_show_logo_on_featured_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='article',
            name='article_status',
            field=models.CharField(
                choices=[('publish', 'publish'), ('draft', 'draft')],
                default='draft',
                max_length=20
            ),
        ),

        # Migrate article status
        migrations.RunPython(set_article_status),
    ]
