# Generated by Django 5.0 on 2024-08-21 13:59

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0135_merge_20240813_1439'),
    ]

    operations = [
        migrations.CreateModel(
            name='BackLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=250)),
                ('url', models.URLField(max_length=250)),
                ('da_score', models.IntegerField()),
                ('follow_unfollow_link', models.Char<PERSON>ield(max_length=100)),
                ('submissions', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('article_link', models.URL<PERSON>ield(max_length=350)),
                ('user', models.ManyToManyField(blank=True, related_name='user_list', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'BackLinks',
            },
        ),
    ]
