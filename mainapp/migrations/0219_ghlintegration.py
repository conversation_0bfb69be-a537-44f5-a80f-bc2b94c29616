# Generated by Django 5.0 on 2025-03-29 06:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0218_merge_20250324_1429'),
    ]

    operations = [
        migrations.CreateModel(
            name='GHLIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_token', models.CharField(max_length=255)),
                ('refresh_token', models.CharField(max_length=255)),
                ('location_id', models.CharField(blank=True, max_length=50)),
                ('site_id', models.Char<PERSON>ield(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('website', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
