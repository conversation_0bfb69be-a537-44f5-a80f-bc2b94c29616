# Generated by Django 5.0 on 2024-02-28 10:29

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0085_allarticlesstats_remove_article_generated_on_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebsiteLogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(max_length=253)),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('connection_type', models.CharField(blank=True, choices=[('connected', 'connected'), ('disconnected', 'disconnected')], max_length=100, null=True)),
                ('message', models.TextField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
