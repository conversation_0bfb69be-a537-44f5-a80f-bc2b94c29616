# Generated by Django 5.0 on 2024-12-20 09:43

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0182_article_crewai_output'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='last_crawled',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='website',
            name='sitemap_urls',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.URLField(), default=list, null=True, size=None),
        ),
    ]
