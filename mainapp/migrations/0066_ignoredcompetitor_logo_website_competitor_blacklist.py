# Generated by Django 4.1.7 on 2023-12-15 08:45

from django.db import migrations, models
import mainapp.models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0065_kubernetesjoblogs'),
    ]

    operations = [
        migrations.CreateModel(
            name='IgnoredCompetitor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(max_length=253, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Logo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('image', models.ImageField(upload_to=mainapp.models.get_logo_upload_to)),
            ],
        ),
        migrations.AddField(
            model_name='website',
            name='competitor_blacklist',
            field=models.JSO<PERSON>ield(default=list),
        ),
    ]
