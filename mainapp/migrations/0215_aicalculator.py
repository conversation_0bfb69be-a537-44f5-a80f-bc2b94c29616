# Generated by Django 5.0 on 2025-03-11 08:23

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0214_remove_googleintegration_user_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AICalculator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calculator_id', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('calc_type', models.CharField(max_length=100)),
                ('html_code', models.TextField()),
                ('conversation', models.J<PERSON>NField()),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
