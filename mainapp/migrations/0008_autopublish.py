# Generated by Django 4.1.7 on 2023-08-10 12:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0007_user_timezone'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutoPublish',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('publish_datetime', models.DateTimeField()),
                ('articles', models.ManyToManyField(to='mainapp.article')),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
