# Generated by Django 5.0 on 2025-04-18 10:43

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0231_aicalculator_script_data'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='aicalculator',
            name='html_code',
        ),
        migrations.CreateModel(
            name='AICalculatorVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_name', models.CharField(max_length=100, unique=True)),
                ('html_code', models.TextField()),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('calculator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.aicalculator')),
            ],
        ),
    ]
