# Generated by Django 5.0 on 2025-04-18 08:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0230_merge_20250418_0948'),
    ]

    operations = [
        migrations.CreateModel(
            name='WordpressPublishedArticle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('post_id', models.IntegerField(db_index=True)),
                ('media_id', models.IntegerField(blank=True, null=True)),
                ('title', models.CharField(max_length=500)),
                ('slug', models.SlugField(max_length=255)),
                ('url', models.URLField()),
                ('published_date', models.DateTimeField()),
                ('gsc_position', models.CharField(max_length=255)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='wordpress_published', to='mainapp.article')),
                ('website', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
            options={
                'ordering': ['-published_date'],
                'unique_together': {('website', 'post_id')},
            },
        ),
    ]
