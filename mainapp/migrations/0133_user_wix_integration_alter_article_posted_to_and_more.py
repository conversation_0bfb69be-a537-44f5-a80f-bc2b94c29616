# Generated by Django 5.0 on 2024-08-09 10:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0132_article_generated_on'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='wix_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.wixintegration'),
        ),
        migrations.AlterField(
            model_name='article',
            name='posted_to',
            field=models.CharField(choices=[('wordpress', 'wordpress'), ('webflow', 'webflow'), ('wix', 'wix')], default='wordpress', max_length=20),
        ),
        migrations.AlterField(
            model_name='howtoarticle',
            name='posted_to',
            field=models.CharField(choices=[('wordpress', 'wordpress'), ('webflow', 'webflow'), ('wix', 'wix')], default='wordpress', max_length=20),
        ),
    ]
