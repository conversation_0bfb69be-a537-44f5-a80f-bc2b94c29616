# Generated by Django 4.1.7 on 2023-12-22 09:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0068_hypestatdata'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='hypestatdata',
            name='domain_authority',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='hypestatdata',
            name='follow',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='hypestatdata',
            name='no_follow',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='hypestatdata',
            name='organic_keywords',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='hypestatdata',
            name='organic_traffic',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='hypestatdata',
            name='referring_domains',
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='hypestatdata',
            name='total_backlinks',
            field=models.PositiveBigIntegerField(null=True),
        ),
    ]
