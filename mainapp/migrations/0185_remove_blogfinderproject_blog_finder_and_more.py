# Generated by Django 5.0 on 2025-01-03 15:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0184_rename_blog_md5_hash_blogfinder_blog_m5_hash'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='blogfinderproject',
            name='blog_finder',
        ),
        migrations.AddField(
            model_name='blogfinderproject',
            name='blog_finder',
            field=models.ManyToManyField(db_index=True, related_name='blog_finder_project', to='mainapp.blogfinder'),
        ),
    ]
