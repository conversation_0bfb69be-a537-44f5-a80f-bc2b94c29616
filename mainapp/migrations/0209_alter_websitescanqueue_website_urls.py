# Generated by Django 5.0 on 2025-02-19 08:44

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0208_website_is_failed_website_task_queued_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='websitescanqueue',
            name='website_urls',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.JSONField(), default=list, null=True, size=None),
        ),
    ]
