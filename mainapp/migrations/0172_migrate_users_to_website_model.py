# Custom migration script created on 2024-12-03 4:48

import secrets
from django.db import migrations, transaction
from django.db.migrations.state import StateApps

import secrets
from django.db import migrations, transaction
from django.db.migrations.state import StateApps


def create_default_website(user, Website):
    """Helper function to create a default website for user"""
    random_string = secrets.token_hex(8)  # 16 character random string
    domain = f"default-{random_string}.xyz"
    
    website = Website.objects.create(
        user=user,
        domain=domain,
        name=f"Default Website - {user.email}",
        protocol="https"
    )
    
    # Set as current active website
    user.current_active_website = website
    user.save(update_fields=['current_active_website'])
    
    return website


def migrate_users_to_website_model(apps: StateApps, _):
    """
    Migrates the data from User model to Website model relationships
    """
    User = apps.get_model('mainapp', 'User')
    Website = apps.get_model('mainapp', 'Website')
    Article = apps.get_model('mainapp', 'Article')
    Keyword = apps.get_model('mainapp', 'Keyword')
    KeywordProject = apps.get_model('mainapp', 'KeywordProject')
    AutomationProject = apps.get_model('mainapp', 'AutomationProject')
    ProgrammaticSeoTitle = apps.get_model('mainapp', 'ProgrammaticSeoTitle')
    WordpressIntegration = apps.get_model('mainapp', 'WordpressIntegration')
    WebflowIntegration = apps.get_model('mainapp', 'WebflowIntegration')
    WixIntegration = apps.get_model('mainapp', 'WixIntegration')
    ShopifyIntegration = apps.get_model('mainapp', 'ShopifyIntegration')

    with transaction.atomic():
        # Process each user
        for user in User.objects.all():
            # Create default website if user doesn't have one
            if not user.current_active_website:
                website = create_default_website(user, Website)
            else:
                website = user.current_active_website

            # Update model relations to point to website instead of user
            Article.objects.filter(user=user).update(website=website)
            Keyword.objects.filter(user=user).update(website=website)
            KeywordProject.objects.filter(user=user).update(website=website)
            AutomationProject.objects.filter(user=user).update(website=website)
            ProgrammaticSeoTitle.objects.filter(user=user).update(website=website)

            # Copy website settings from user to website model
            website_fields = [
                'keyword_strategy', 'image_source', 'feature_image_template_id',
                'feature_image_required', 'generate_bannerbear_featured_image',
                'article_tone_of_voice', 'article_language_preference',
                'external_backlinks_preference', 'max_internal_backlinks',
                'max_external_backlinks', 'ai_generated_image_style',
                'images_file_format'
            ]

            for field in website_fields:
                if hasattr(user, field):
                    setattr(website, field, getattr(user, field))
            
            website.save()

            # Update Integration relations
            for integration_model in [WordpressIntegration, WebflowIntegration, WixIntegration, ShopifyIntegration]:
                integration_name = integration_model.__name__.lower().replace('integration', '')
                relation_name = f"{integration_name}_integrations"
                
                # Update the website field for all integrations
                integration_model.objects.filter(
                    id__in=getattr(user, relation_name).values_list('id', flat=True)
                ).update(website=website)


def reverse_migrate(apps: StateApps, _):
    """
    Reverse migration - moves data back from Website to User model
    """
    User = apps.get_model('mainapp', 'User')
    Website = apps.get_model('mainapp', 'Website')

    with transaction.atomic():
        for website in Website.objects.all():
            user = website.user
            if not user:
                continue

            # Copy website settings back to user
            user_fields = [
                'keyword_strategy', 'image_source', 'feature_image_template_id',
                'feature_image_required', 'generate_bannerbear_featured_image',
                'article_tone_of_voice', 'article_language_preference',
                'external_backlinks_preference', 'max_internal_backlinks',
                'max_external_backlinks', 'ai_generated_image_style',
                'images_file_format'
            ]

            for field in user_fields:
                if hasattr(website, field):
                    setattr(user, field, getattr(website, field))
            
            user.save()


class Migration(migrations.Migration):
    dependencies = [
        ('mainapp', '0171_remove_article_user_remove_automationproject_user_and_more'),
    ]

    operations = [
        migrations.RunPython(
            migrate_users_to_website_model,
            reverse_code=reverse_migrate
        ),
    ]
