# Generated by Django 5.0 on 2024-11-26 15:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0165_wordpresscategories'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShopifyIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_url', models.URLField()),
                ('access_token', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='shopify_integrations',
            field=models.ManyToManyField(to='mainapp.shopifyintegration'),
        ),
    ]
