# Generated by Django 5.0 on 2024-05-21 08:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0111_merge_20240423_1536'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='website',
            name='auto_publish_days',
            field=models.CharField(choices=[('all_days', 'All Days'), ('weekdays', 'Weekdays (Monday to Friday)'), ('weekends', 'Weekends (Saturday & Sunday)'), ('alternate_days', 'Alternate Days'), ('schedule_randomly', 'Schedule Randomly')], default='all_days', max_length=300),
        ),
        migrations.AlterField(
            model_name='website',
            name='auto_publish_time',
            field=models.CharField(choices=[('morning', 'Morning (06:00 AM to 12:00 PM)'), ('afternoon', 'Afternoon (12:00 PM to 06:00 PM)'), ('evening', 'Evening (06:00 PM to 12:00 AM)'), ('night', 'Night (12:00 AM to 06:00 AM)'), ('post_anytime', 'Randomly')], default='morning', max_length=300),
        ),
    ]
