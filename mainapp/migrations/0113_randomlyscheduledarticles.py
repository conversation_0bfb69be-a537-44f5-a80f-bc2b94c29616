# Generated by Django 5.0 on 2024-05-21 14:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0112_alter_website_auto_publish_days_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RandomlyScheduledArticles',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateTimeField()),
                ('articles_count', models.IntegerField()),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
