# Generated by Django 5.0 on 2024-02-29 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0085_allarticlesstats_remove_article_generated_on_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlockDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'Blocked Domains',
            },
        ),
    ]
