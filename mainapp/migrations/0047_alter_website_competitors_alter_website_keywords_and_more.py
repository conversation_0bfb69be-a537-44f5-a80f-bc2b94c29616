# Generated by Django 4.1.7 on 2023-11-03 13:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0046_remove_titlegenkeywords_used_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='website',
            name='competitors',
            field=models.ManyToManyField(db_index=True, to='mainapp.competitor'),
        ),
        migrations.AlterField(
            model_name='website',
            name='keywords',
            field=models.ManyToManyField(db_index=True, to='mainapp.keyword'),
        ),
        migrations.AlterField(
            model_name='website',
            name='selected_keywords',
            field=models.ManyToManyField(db_index=True, related_name='selected_by_website', to='mainapp.keyword'),
        ),
        migrations.AlterField(
            model_name='website',
            name='user_added_keywords',
            field=models.ManyToManyField(db_index=True, related_name='user_added', to='mainapp.keyword'),
        ),
    ]
