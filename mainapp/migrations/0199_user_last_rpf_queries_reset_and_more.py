# Generated by Django 5.0 on 2025-01-27 14:09

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0198_remove_redditpostfinderresult_hypestat_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='last_rpf_queries_reset',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='user',
            name='rpf_queries_generated',
            field=models.IntegerField(default=0),
        ),
    ]
