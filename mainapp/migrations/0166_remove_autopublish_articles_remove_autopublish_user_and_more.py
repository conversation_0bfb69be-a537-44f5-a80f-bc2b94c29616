# Generated by Django 5.0 on 2024-11-23 12:05

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0165_wordpresscategories'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='autopublish',
            name='articles',
        ),
        migrations.RemoveField(
            model_name='autopublish',
            name='user',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='article_images',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='featured_images',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='keyword',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='selected_featured_image',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='user',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='article_images',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='featured_images',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='keyword',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='selected_featured_image',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='user',
        ),
        migrations.DeleteModel(
            name='OpenaiTimeoutLog',
        ),
        migrations.DeleteModel(
            name='ProTip',
        ),
        migrations.RemoveField(
            model_name='randomlyscheduledarticles',
            name='website',
        ),
        migrations.RemoveField(
            model_name='titlegenkeywords',
            name='keyword',
        ),
        migrations.RemoveField(
            model_name='titlegenkeywords',
            name='user',
        ),
        migrations.RemoveField(
            model_name='titlegenkeywords',
            name='website',
        ),
        migrations.RemoveField(
            model_name='websiteindexation',
            name='user',
        ),
        migrations.RemoveField(
            model_name='websiteindexation',
            name='website',
        ),
        migrations.RenameField(
            model_name='website',
            old_name='ListicleKeywords',
            new_name='listicle_keywords',
        ),
        migrations.RemoveField(
            model_name='user',
            name='conn_site_reminder_email_sent',
        ),
        migrations.RemoveField(
            model_name='user',
            name='google_analytics_integrated',
        ),
        migrations.RemoveField(
            model_name='user',
            name='google_drive_integrated',
        ),
        migrations.RemoveField(
            model_name='website',
            name='ai_generated_image_style',
        ),
        migrations.RemoveField(
            model_name='website',
            name='article_language_preference',
        ),
        migrations.RemoveField(
            model_name='website',
            name='article_tone_of_voice',
        ),
        migrations.RemoveField(
            model_name='website',
            name='auto_publish',
        ),
        migrations.RemoveField(
            model_name='website',
            name='auto_publish_article_count',
        ),
        migrations.RemoveField(
            model_name='website',
            name='auto_publish_days',
        ),
        migrations.RemoveField(
            model_name='website',
            name='auto_publish_email_hours',
        ),
        migrations.RemoveField(
            model_name='website',
            name='auto_publish_time',
        ),
        migrations.RemoveField(
            model_name='website',
            name='competitor_blacklist',
        ),
        migrations.RemoveField(
            model_name='website',
            name='content_plan_generation_status',
        ),
        migrations.RemoveField(
            model_name='website',
            name='content_plan_performance_chart_url',
        ),
        migrations.RemoveField(
            model_name='website',
            name='content_plan_started_on',
        ),
        migrations.RemoveField(
            model_name='website',
            name='content_plan_task_data',
        ),
        migrations.RemoveField(
            model_name='website',
            name='content_plan_task_progress',
        ),
        migrations.RemoveField(
            model_name='website',
            name='current_integration',
        ),
        migrations.RemoveField(
            model_name='website',
            name='domain_authority',
        ),
        migrations.RemoveField(
            model_name='website',
            name='external_backlinks_preference',
        ),
        migrations.RemoveField(
            model_name='website',
            name='feature_image_template_id',
        ),
        migrations.RemoveField(
            model_name='website',
            name='feature_image_text_required',
        ),
        migrations.RemoveField(
            model_name='website',
            name='follow_count',
        ),
        migrations.RemoveField(
            model_name='website',
            name='generate_bannerbear_featured_image',
        ),
        migrations.RemoveField(
            model_name='website',
            name='google_analytics_integrated',
        ),
        migrations.RemoveField(
            model_name='website',
            name='google_drive_integrated',
        ),
        migrations.RemoveField(
            model_name='website',
            name='image_source',
        ),
        migrations.RemoveField(
            model_name='website',
            name='images_file_format',
        ),
        migrations.RemoveField(
            model_name='website',
            name='keyword_strategy',
        ),
        migrations.RemoveField(
            model_name='website',
            name='max_external_backlinks',
        ),
        migrations.RemoveField(
            model_name='website',
            name='max_internal_backlinks',
        ),
        migrations.RemoveField(
            model_name='website',
            name='no_follow_count',
        ),
        migrations.RemoveField(
            model_name='website',
            name='organic_keywords',
        ),
        migrations.RemoveField(
            model_name='website',
            name='organic_traffic',
        ),
        migrations.RemoveField(
            model_name='website',
            name='referring_domains_count',
        ),
        migrations.RemoveField(
            model_name='website',
            name='title_gen_cll_head',
        ),
        migrations.RemoveField(
            model_name='website',
            name='title_gen_processing',
        ),
        migrations.RemoveField(
            model_name='website',
            name='top_keywords_by_volume',
        ),
        migrations.RemoveField(
            model_name='website',
            name='total_backlinks',
        ),
        migrations.RemoveField(
            model_name='website',
            name='webflow_integration',
        ),
        migrations.RemoveField(
            model_name='website',
            name='wix_integration',
        ),
        migrations.RemoveField(
            model_name='website',
            name='wordpress_integration',
        ),
        migrations.DeleteModel(
            name='AutoPublish',
        ),
        migrations.DeleteModel(
            name='HowToArticle',
        ),
        migrations.DeleteModel(
            name='Listicle',
        ),
        migrations.DeleteModel(
            name='RandomlyScheduledArticles',
        ),
        migrations.DeleteModel(
            name='TitleGenKeywords',
        ),
        migrations.DeleteModel(
            name='WebsiteIndexation',
        ),
    ]
