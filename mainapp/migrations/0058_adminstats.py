# Generated by Django 4.1.7 on 2023-11-22 12:15

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0057_remove_website_integration_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('total_signups', models.IntegerField(null=True)),
                ('total_paid_customer', models.IntegerField(null=True)),
                ('total_free_customers', models.IntegerField(null=True)),
                ('today_signup', models.IntegerField(null=True)),
            ],
        ),
    ]
