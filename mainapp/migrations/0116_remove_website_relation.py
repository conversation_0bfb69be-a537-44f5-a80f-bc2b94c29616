# Custom migration script created on 2024-07-10 12:00

import django
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('mainapp', '0115_migrate_users_and_settings'),
    ]

    operations = [
        # Remove website relation
        migrations.RemoveField(
            model_name='article',
            name='website',
        ),
        migrations.RemoveField(
            model_name='autopublish',
            name='website',
        ),
        migrations.RemoveField(
            model_name='gpt4usagestats',
            name='website',
        ),
        migrations.RemoveField(
            model_name='howtoarticle',
            name='website',
        ),
        migrations.RemoveField(
            model_name='kubernetesjob',
            name='website',
        ),
        migrations.RemoveField(
            model_name='listicle',
            name='website',
        ),
        migrations.AddField(
            model_name='competitor',
            name='associated_keyword_project',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.keywordproject'),
        )
    ]
