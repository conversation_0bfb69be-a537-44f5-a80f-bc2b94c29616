# Generated by Django 5.0 on 2024-12-27 10:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0183_glossarytopic_website'),
    ]

    operations = [
        migrations.CreateModel(
            name='GlossaryContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_id', models.CharField(max_length=255)),
                ('topic', models.CharField(max_length=255)),
                ('term', models.Char<PERSON>ield(max_length=255)),
                ('content', models.TextField()),
                ('keyword_hash', models.CharField(max_length=32, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.<PERSON>Key(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
