# Generated by Django 5.0 on 2024-08-20 08:30

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0135_merge_20240813_1439'),
    ]

    operations = [
        migrations.CreateModel(
            name='SerperResults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword_hash', models.CharField(max_length=50)),
                ('result', models.JSONField()),
            ],
        ),
        migrations.AddField(
            model_name='article',
            name='article_outlines',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(), default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='article',
            name='merged_summary',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='article',
            name='summary_inputs',
            field=models.JSO<PERSON>ield(blank=True, null=True),
        ),
    ]
