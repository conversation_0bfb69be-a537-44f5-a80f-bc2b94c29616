from django.db import migrations
from mainapp.models import generate_plugin_uid

def populate_verification_tokens(apps, schema_editor):
    AICalculator = apps.get_model('mainapp', 'AICalculator')
    for calculator in AICalculator.objects.filter(verification_token__isnull=True):
        unique_token = f"calc-verify-{generate_plugin_uid()}"
        # Ensure uniqueness explicitly
        while AICalculator.objects.filter(verification_token=unique_token).exists():
            unique_token = f"calc-verify-{generate_plugin_uid()}"
        calculator.verification_token = unique_token
        calculator.save()

class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0217_aicalculator_is_verified_and_more'),
    ]

    operations = [
        migrations.RunPython(populate_verification_tokens),
    ]
