# Generated by Django 5.0 on 2024-01-11 17:39

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0071_merge_20240109_1321'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='google_analytics_integrated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='website',
            name='google_search_console_integrated',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='GoogleIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('integration_type', models.CharField(choices=[('google-search-console', 'Google Search Console'), ('google-analytics', 'Google Analytics')], max_length=50)),
                ('token', models.CharField(max_length=300)),
                ('refresh_token', models.CharField(max_length=150)),
                ('token_uri', models.CharField(max_length=50)),
                ('client_id', models.CharField(max_length=100)),
                ('client_secret', models.CharField(max_length=50)),
                ('scopes', django.contrib.postgres.fields.ArrayField(base_field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=150), size=None), size=None)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
