# Generated by Django 5.0 on 2024-02-13 10:02

import django.utils.timezone
import mainapp.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0080_merge_20240209_1527'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArticleImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=mainapp.models.get_article_image_upload_to)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.AddField(
            model_name='article',
            name='article_images',
            field=models.ManyToManyField(to='mainapp.articleimage'),
        ),
        migrations.AddField(
            model_name='howtoarticle',
            name='article_images',
            field=models.ManyToManyField(to='mainapp.articleimage'),
        ),
    ]
