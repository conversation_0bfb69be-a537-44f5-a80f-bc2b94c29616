# Generated by Django 5.0 on 2024-07-11 15:37

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0122_rename_blockkeywords_blockconnectwebsitekeywords'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutomationProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_id', models.CharField(db_index=True, max_length=300, unique=True)),
                ('project_name', models.CharField(blank=True, default='Automation Project', max_length=300, null=True)),
                ('keywords_traffic_range_min', models.PositiveBigIntegerField(default=0)),
                ('keywords_traffic_range_max', models.PositiveBigIntegerField(default=0)),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default=None, max_length=300, null=True)),
                ('article_count', models.PositiveIntegerField(default=2)),
                ('selected_integration', models.CharField(choices=[('wordpress', 'WordPress'), ('webflow', 'Webflow')], default=None, max_length=300, null=True)),
                ('auto_publish_state', models.CharField(choices=[('on', 'ON'), ('draft', 'DRAFT'), ('off', 'OFF'), ('paused', 'PAUSED')], default='off', max_length=100)),
                ('auto_publish_days', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], max_length=300), default=list, size=None)),
                ('auto_publish_time', models.CharField(choices=[('any_time', 'Any Time'), ('morning', 'Morning (06:00 AM to 12:00 PM)'), ('afternoon', 'Afternoon (12:00 PM to 06:00 PM)'), ('evening', 'Evening (06:00 PM to 12:00 AM)'), ('night', 'Night (12:00 AM to 06:00 AM)')], default='any_time', max_length=300)),
                ('auto_publish_email_hours', models.PositiveIntegerField(default=1)),
                ('publish_only_generated_articles', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('associated_keyword_project', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.keywordproject')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
