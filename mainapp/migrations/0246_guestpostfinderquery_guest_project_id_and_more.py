# Generated by Django 5.0 on 2025-05-30 16:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0245_merge_20250529_1524'),
    ]

    operations = [
        migrations.AddField(
            model_name='guestpostfinderquery',
            name='guest_project_id',
            field=models.CharField(blank=True, db_index=True, max_length=300, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='hypestatdata',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='hypestatdata',
            name='domain',
            field=models.CharField(db_index=True, max_length=253),
        ),
        migrations.AlterUniqueTogether(
            name='hypestatdata',
            unique_together={('website', 'domain')},
        ),
    ]
