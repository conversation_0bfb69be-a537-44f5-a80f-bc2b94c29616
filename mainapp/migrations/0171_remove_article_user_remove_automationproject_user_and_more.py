# Generated by Django 5.0 on 2024-12-03 13:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0170_merge_20241129_1203'),
    ]

    operations = [
        migrations.AddField(
            model_name='article',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='automationproject',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='keyword',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='keywordproject',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='programmaticseotitle',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='programmatic_seo_titles', to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='shopifyintegration',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='webflowintegration',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='website',
            name='ai_generated_image_style',
            field=models.CharField(default='illustration', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='article_language_preference',
            field=models.CharField(default='english', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='article_tone_of_voice',
            field=models.CharField(default='exciting', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='external_backlinks_preference',
            field=models.CharField(default='no-follow', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='feature_image_required',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='website',
            name='feature_image_template_id',
            field=models.CharField(choices=[('neon-style-with-text', 'Neon Style With Text'), ('neon-style-without-text', 'Neon Style Without Text'), ('water-color-with-text', 'Water Color With Text'), ('water-color-without-text', 'Water Color Without Text'), ('retro-with-text', 'Retro With Text'), ('retro-without-text', 'Retro Without Text'), ('comic-style-with-text', 'Comic Style With Text'), ('comic-style-without-text', 'Comic Style Without Text'), ('doodle-sketch-with-text', 'Doodle Sketch With Text'), ('doodle-sketch-without-text', 'Doodle Sketch Without Text'), ('cyberpunk-with-text', 'Cyberpunk With Text'), ('cyberpunk-without-text', 'Cyberpunk Without Text'), ('grunge-with-text', 'Grunge With Text'), ('grunge-without-text', 'Grunge Without Text'), ('water-color-with-doodle-with-text', 'Water With Doodle With Text'), ('water-color-with-doodle-without-text', 'Water With Doodle Without Text'), ('ok0l2K5mppOLZ3j1Yx', 'Elegant Minimalism'), ('j14WwV5Vjj4R5a7XrB', 'Compact Image Focus'), ('7wpnPQZzKKOm5dOgxo', 'Bold Blue Contrast'), ('yKBqAzZ9xwB0bvMx36', 'Image Dominance'), ('kY4Qv7D8dAaLDB0qmP', 'Bright Minimalism')], default='water-color-with-text', max_length=100),
        ),
        migrations.AddField(
            model_name='website',
            name='generate_bannerbear_featured_image',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='website',
            name='image_source',
            field=models.CharField(choices=[('no_image', 'No Image'), ('unsplash', 'Unsplash'), ('google', 'Google Images'), ('ai_image_generation', 'AI Image Generation')], default='no_image', max_length=100),
        ),
        migrations.AddField(
            model_name='website',
            name='images_file_format',
            field=models.CharField(choices=[('webp', 'WebP'), ('png', 'PNG'), ('jpeg', 'JPEG')], default='png', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='keyword_strategy',
            field=models.CharField(choices=[('volume', 'Volume Based'), ('cpc', 'CPC Based'), ('competition', 'Competition Based')], default='cpc', max_length=50),
        ),
        migrations.AddField(
            model_name='website',
            name='max_external_backlinks',
            field=models.PositiveIntegerField(default=2),
        ),
        migrations.AddField(
            model_name='website',
            name='max_internal_backlinks',
            field=models.PositiveIntegerField(default=5),
        ),
        migrations.AddField(
            model_name='wixintegration',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
        migrations.AddField(
            model_name='wordpressintegration',
            name='website',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website'),
        ),
    ]
