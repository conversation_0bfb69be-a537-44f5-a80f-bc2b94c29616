# Generated by Django 5.0 on 2024-09-23 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0149_rename_feature_image_text_required_user_feature_image_required'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='feature_image_template_id',
            field=models.CharField(choices=[('neon-style-with-text', 'Neon Style With Text'), ('neon-style-without-text', 'Neon Style Without Text'), ('water-color-with-text', 'Water Color With Text'), ('water-color-without-text', 'Water Color Without Text'), ('retro-with-text', 'Retro With Text'), ('retro-without-text', 'Retro Without Text'), ('comic-style-with-text', 'Comic Style With Text'), ('comic-style-without-text', 'Comic Style Without Text'), ('doodle-sketch-with-text', 'Doodle Sketch With Text'), ('doodle-sketch-without-text', 'Doodle Sketch Without Text'), ('cyberpunk-with-text', 'Cyberpunk With Text'), ('cyberpunk-without-text', 'Cyberpunk Without Text'), ('ok0l2K5mppOLZ3j1Yx', 'Custom Image 1'), ('j14WwV5Vjj4R5a7XrB', 'Custom Image 2'), ('7wpnPQZzKKOm5dOgxo', 'Custom Image 3'), ('yKBqAzZ9xwB0bvMx36', 'Custom Image 4'), ('kY4Qv7D8dAaLDB0qmP', 'Custom Image 5'), ('j14WwV5Vjj4R5a7XrB', 'Custom Image 6')], default='neon-style-with-text', max_length=30),
        ),
    ]
