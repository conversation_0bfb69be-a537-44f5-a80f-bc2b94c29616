# Generated by Django 5.0 on 2025-03-03 13:23

import django.db.models.deletion
from django.db import migrations, models

from mainapp.models import GoogleIntegration

def migrate_user_to_website(apps, _):
    google_integration: GoogleIntegration = apps.get_model('mainapp', 'GoogleIntegration')

    for integration in google_integration.objects.all():
        if integration.user and integration.user.current_active_website:
            integration.website = integration.user.current_active_website
            integration.save()

def migrate_website_to_user(apps, _):
    google_integration: GoogleIntegration = apps.get_model('mainapp', 'GoogleIntegration')

    for integration in google_integration.objects.all():
        if integration.website and integration.website.user:
            integration.user = integration.website.user
            integration.save()

class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0213_websitescanqueue_request_from_admin'),
    ]

    operations = [
        migrations.AddField(
            model_name='googleintegration',
            name='website',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website', null=True, default=None),
        ),

        migrations.RunPython(migrate_user_to_website, reverse_code=migrate_website_to_user),

        migrations.RemoveField(
            model_name='googleintegration',
            name='user',
        ),
        migrations.RemoveField(
            model_name='user',
            name='google_search_console_integrated',
        ),
    ]