# Generated by Django 5.0 on 2024-03-20 11:33

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0103_remove_website_webflow_integration_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebflowIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('based_on', models.CharField(choices=[('app', 'App Based'), ('api', 'API Based')], default='app', max_length=10)),
                ('token', models.CharField(max_length=300)),
                ('site_id', models.CharField(max_length=100)),
                ('site_url', models.TextField()),
                ('collection_id', models.CharField(max_length=100)),
                ('collection_slug', models.Char<PERSON><PERSON>(max_length=120, null=True)),
                ('collection_fields', models.JSONField()),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.AddField(
            model_name='website',
            name='webflow_integration',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.webflowintegration'),
        ),
    ]
