# Generated by Django 5.0 on 2024-09-09 14:29

from django.db import migrations, models, transaction
from django.db.migrations.state import StateApps

from mainapp.models import User


def migrate_users_webflow_integration(apps: StateApps, _):
    """
    Migrate users webflow integration to new field
    :param apps: Django StateApps
    """
    with transaction.atomic():
        user_model: User = apps.get_model('mainapp', "User")
        users: models.QuerySet[User] = user_model.objects.filter(webflow_integration__isnull=False)

        for user in users.all():
            user.webflow_integrations.add(user.webflow_integration)


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0142_merge_20240830_1359'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='webflow_integrations',
            field=models.ManyToManyField(to='mainapp.webflowintegration'),
        ),

        # Migrate user webflow integration to new field
        migrations.RunPython(migrate_users_webflow_integration),

         migrations.RemoveField(
            model_name='user',
            name='webflow_integration',
        ),
    ]
