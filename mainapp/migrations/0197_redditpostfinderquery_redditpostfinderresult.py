# Generated by Django 5.0 on 2025-01-27 08:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0196_merge_20250125_1556'),
    ]

    operations = [
        migrations.CreateModel(
            name='RedditPostFinderQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=250)),
                ('limit', models.IntegerField()),
                ('is_processing', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('website', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
        migrations.CreateModel(
            name='RedditPostFinderResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('post_title', models.CharField(blank=True, max_length=500)),
                ('post_link', models.CharField(blank=True, max_length=500)),
                ('hypestat', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.hypestatdata')),
                ('reddit_post_finder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.redditpostfinderquery')),
            ],
        ),
    ]
