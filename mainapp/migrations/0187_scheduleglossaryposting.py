# Generated by Django 5.0 on 2024-12-31 08:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0186_rename_created_on_glossarycontent_created_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScheduleGlossaryPosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schedule_datetime', models.DateTimeField(auto_now_add=True)),
                ('schedule_on', models.DateTimeField(auto_now_add=True)),
                ('selected_integration_name', models.CharField(default=None, null=True)),
                ('selected_integration_unique_text_id', models.TextField(default=None, null=True)),
                ('post_status', models.<PERSON>r<PERSON><PERSON>(choices=[('draft', 'Draft'), ('publish', 'Publish')], default='publish', max_length=10)),
                ('glossary', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='mainapp.glossarycontent')),
            ],
        ),
    ]
