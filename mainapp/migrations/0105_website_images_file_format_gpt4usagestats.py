# Generated by Django 5.0 on 2024-03-21 16:12

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0104_webflowintegration_website_webflow_integration'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='images_file_format',
            field=models.CharField(choices=[('webp', 'WebP'), ('png', 'PNG'), ('jpeg', 'JPEG')], default='png', max_length=50),
        ),
        migrations.CreateModel(
            name='GPT4UsageStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('usage_type', models.Char<PERSON>ield(max_length=100)),
                ('usage_cost', models.DecimalField(decimal_places=6, default=0.0, max_digits=10)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.website')),
            ],
        ),
    ]
