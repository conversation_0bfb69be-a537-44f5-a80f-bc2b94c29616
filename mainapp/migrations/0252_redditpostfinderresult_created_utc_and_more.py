# Generated by Django 5.0 on 2025-06-25 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0251_website_toggle_bullet_points_website_toggle_faq_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='created_utc',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='num_comments',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='reddit_content',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='subreddit_name',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='subreddit_subscribers',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='upvote_ratio',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='redditpostfinderresult',
            name='upvote_score',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
