# THIS FILE CONTAINS CUSTOM JsonResponse CLASSES
# Use this to avoid writing/calling base JsonResponse class with same status & data in multiple places
# to conform with DRY practices

from django.http.response import JsonResponse


class JsonResponseBadRequest(JsonResponse):
    def __init__(self, additional_data: dict = None):
        default_data = {'message': "Bad Request"}
        data = {**default_data, **additional_data} if additional_data else default_data
        super(JsonResponseBadRequest, self).__init__(status=400, data=data)


class JsonResponse404(JsonResponse):
    def __init__(self, additional_data: dict = None):
        default_data = {'message': "Resource Not Found"}
        data = {**default_data, **additional_data} if additional_data else default_data
        super(JsonResponse404, self).__init__(status=404, data=data)


class JsonResponseKeyError(JsonResponse):
    def __init__(self):
        super(JsonResponseKeyError, self).__init__(status=400, data={'message': "Missing required keys in POST data"})


class JsonResponseUnauthorized(JsonResponse):
    def __init__(self):
        super(JsonResponseUnauthorized, self).__init__(status=401, data={'message': "Unauthorized"})


class JsonResponseServerError(JsonResponse):
    def __init__(self):
        super(JsonResponseServerError, self).__init__(status=500, data={'message': "Server Error"})


class JsonResponseRedirect(JsonResponse):
    def __init__(self, redirect_to: str):
        super(JsonResponseRedirect, self).__init__(status=302, data={'redirect_to': redirect_to})


class JsonResponseGone(JsonResponse):
    def __init__(self, additional_data: dict):
        default_data = {'message': "Required Resource Unavailable"}
        data = {**default_data, **additional_data} if additional_data else default_data
        super(JsonResponseGone, self).__init__(status=310, data=data)
