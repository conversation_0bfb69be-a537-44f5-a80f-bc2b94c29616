import asyncio
import logging
from typing import List, Dict

from django.db import transaction
from langchain_community.callbacks.manager import get_openai_callback
from langchain_core.runnables.base import RunnableSequence
from langchain_openai import ChatOpenAI
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from pydantic import BaseModel, Field

from mainapp.models import User, Keyword, Article, GPT4UsageStats, SerperResults
from mainapp.utils import generate_article_uid, unescape_amp_char

from mainapp.article_title_gen_v2.prompts import title_generation_prompt_text
from mainapp.article_title_gen_v2.serper_scraper4_3 import find_article_links
from mainapp.article_title_gen_v2.serper_scraper4_3 import generate_titles_from_serper


logger = logging.getLogger('abun.title_generation')

class TitleData(BaseModel):
    title: str = Field(description="long-tail blog title")
    inspired_by_title: str = Field(description="One of the example title sentences that served as inspiration for the generated title.")
    keyword: str = Field(description="Keyword used to generate this title")


class Titles(BaseModel):
    titles: List[TitleData] = Field(description="List of generated titles")

class ArticleTitleGeneratorV2:
    def __init__(self, user: User):
        self._user = user
        self._website = user.current_active_website
        self._max_titles_per_prompt = 20
        self._gpt_llm = ChatOpenAI(
            # model_name="gpt-4o",
            model_name="gpt-4.1",
            temperature=0.7
        )

    def _get_title_generation_chain(self) -> RunnableSequence:
        """
        Generates and returns a RunnableSequence object for title generation.
        """
        titlegen_parser = PydanticOutputParser(pydantic_object=Titles)
        titlegen_prompt = PromptTemplate(
            template=title_generation_prompt_text(),
            input_variables=self.title_gen_input_variables,
            partial_variables={"format_instructions": titlegen_parser.get_format_instructions()},
        )
        chain = titlegen_prompt | self._gpt_llm | titlegen_parser
        return chain

    @staticmethod
    async def _generate_async(chain: RunnableSequence, input_dict: Dict):
        """
        Runs chain as async and returns response.
        """
        try:
            resp = await chain.ainvoke(input_dict)
        except Exception:
            logger.debug("[*] Article Title Generation: Previous attempt failed. Trying again...")
            resp = await chain.ainvoke(input_dict)

        return resp

    async def _generate_titles_async(self, inputs: List[Dict]) -> Dict:
        """
        Generate titles asynchronously using ChatGPT.
        
        :param inputs: List of input dictionaries for title generation.
        :param type_of_generation: Type of article titles generation to perform. Can be "default", or "How-to".
        :returns: A dictionary containing a list of titles and cost of usage.
        """
        title_chain = self._get_title_generation_chain()

        with get_openai_callback() as cb:
            try:
                tasks = [self._generate_async(title_chain, input_dict) for input_dict in inputs]
            except Exception as e:
                logger.error(f"Error in generating titles: {e}")
                return {"results": [], "cost": 0}
            try:
                futures = await asyncio.gather(*tasks)
                results = []
                for future in futures:
                    for title_data in future.titles:
                        if title_data.title not in [result['title'] for result in results]:
                            results.append({
                                'keyword': title_data.keyword,
                                'title': title_data.title,
                                'inspired_by_title': title_data.inspired_by_title,
                            })

                formatted_cost = f"{cb.total_cost:.20f}"
                logger.debug(f"[*] Article Title Generation cost: {formatted_cost}")
                return {"results": results, "cost": formatted_cost}
            except Exception as e:
                formatted_cost = f"{cb.total_cost:.20f}"
                logger.error(f"Error in generating titles: {e}, {formatted_cost}")
                return {"results": [], "cost": formatted_cost}

    def prepare_inputs(self, keyword_hash, location, title_count) -> Dict:
        keyword_object = Keyword.objects.get(keyword_md5_hash=keyword_hash)
        serper_results = find_article_links(keyword_object.keyword, location, title_count)
        other_top_ranking_urls = [result["url"] for result in serper_results[:3]]

        if not other_top_ranking_urls:
            raise ValueError(f"No SERP results found for '{keyword_hash}' keyword hash to generate titles.")
            
        inputs = [{
            "keyword": keyword_object.keyword,
            "example_title_sentences": '\n\n'.join(f'"{title}"' for title in [result["title"] for result in serper_results]),
            "language": self._website.article_language_preference,
        }]

        self.title_gen_input_variables = ["keyword", "example_title_sentences","language"]

        # Save serper scraper results for admin panel
        if not SerperResults.objects.filter(keyword_hash=keyword_object.keyword_md5_hash).exists():
            SerperResults.objects.create(keyword_hash=keyword_object.keyword_md5_hash, result=serper_results)

        return {
            "inputs": inputs,
            "serper_results": serper_results,
            "other_top_ranking_urls": other_top_ranking_urls,
            "keyword_object": keyword_object,
        }


    def save_titles(self, results: List[Dict], serper_results: List[Dict], keyword_object: Keyword, other_top_ranking_urls: List[str], type_of_generation: str) -> List[str]:
        list_of_titles = []
        article_bulk_create = []

        for title_data in results:
            title = unescape_amp_char(title_data['title'].strip())
            title_inspired_from = None

            for result in serper_results:
                if title_data['inspired_by_title'] == result['title'].strip():
                    title_inspired_from = result['url']
                    break

            if title in list_of_titles or (
                type_of_generation == "default"
                and self._user.articles.filter(title=title).exists()
            ):
                continue

            if title_inspired_from is None:
                article_bulk_create.append(Article(
                    article_uid=generate_article_uid(self._user.username),
                    website=self._website,
                    title=title,
                    keyword=keyword_object,
                    other_top_ranking_urls=other_top_ranking_urls,
                ))

            else:
                other_top_ranking_urls_count = len(other_top_ranking_urls)
                top_2_ranking_urls = other_top_ranking_urls[:2]

                if title_inspired_from in top_2_ranking_urls:
                    top_2_ranking_urls.append(other_top_ranking_urls[-1])
                    top_2_ranking_urls.pop(top_2_ranking_urls.index(title_inspired_from))

                if other_top_ranking_urls_count == 1:
                    article_bulk_create.append(Article(
                        article_uid=generate_article_uid(self._user.username),
                        website=self._website,
                        title=title,
                        keyword=keyword_object,
                        other_top_ranking_urls=[title_inspired_from]
                    ))

                elif other_top_ranking_urls == 2:
                    article_bulk_create.append(Article(
                        article_uid=generate_article_uid(self._user.username),
                        website=self._website,
                        title=title,
                        keyword=keyword_object,
                        other_top_ranking_urls=[top_2_ranking_urls[0], title_inspired_from]
                    ))

                else:
                    article_bulk_create.append(Article(
                        article_uid=generate_article_uid(self._user.username),
                        website=self._website,
                        title=title,
                        keyword=keyword_object,
                        other_top_ranking_urls=[top_2_ranking_urls[0], top_2_ranking_urls[1], title_inspired_from]
                    ))

            list_of_titles.append(title)

        Article.objects.bulk_create(article_bulk_create)
        self._user.save()

        return list_of_titles

    @transaction.atomic
    def generate_titles(self, keyword_hash, location: str = "us", title_count: int = 10, type_of_generation: str = "default") -> List[str]:
        """
        Generate given number of titles for the website using ChatGPT.

        :param keyword_hash: MD5 hash of the keyword to generate titles for.
        :param location: Location to get keyword volume data from.
        :param title_count: Number of titles to generate.
        :param type_of_generation: Type of article titles generation to perform. Can be "default", or "How-to".
        :returns: List of titles
        """
        try:
            keyword_object = Keyword.objects.get(keyword_md5_hash=keyword_hash)

            serper_results: List[Dict] = find_article_links(keyword_object.keyword, location, title_count)

            logger.debug(f"[*] Total Filtered Serper results to feed to gpt: {len(serper_results)}")

            other_top_ranking_urls = [result["url"] for result in serper_results][:3] # Get top 3 urls

            if not other_top_ranking_urls:
                raise ValueError(f"No SERP results found for '{keyword_hash}' keyword hash to generate titles.")

            # Save serper scraper results for admin panel
            if not SerperResults.objects.filter(keyword_hash=keyword_object.keyword_md5_hash).exists():
                SerperResults.objects.create(keyword_hash=keyword_object.keyword_md5_hash, result=serper_results)

            # ------------------ generate titles by running concurrent prompts for the keyword ------------------
            list_of_titles: List[str] = []
            inputs: List[Dict] = []
            logger.debug(f"> Prepping title generation V2 prompt with Keyword: {keyword_object.keyword}")
            inputs.append({
                "keyword": keyword_object.keyword,
                "example_title_sentences": '\n\n'.join(f'"{title}"' for title in [result["title"] for result in serper_results]),
                "language": self._website.article_language_preference,
            })

            self.title_gen_input_variables = ["keyword", "example_title_sentences","language"]

            # --------------------------- RUN TITLE GENERATION PROMPTS ---------------------------
            result = asyncio.run(self._generate_titles_async(inputs))  # [{keyword, title}, ...]
            results: List[Dict] = result['results']
            logger.debug(f"[*] Article Title Generation: Generated {len(results)} titles")

            # --------------------------- SAVE USAGE STATS TO DATABASE ---------------------------
            GPT4UsageStats.objects.create(
                user=self._user,
                usage_type="title_generation",
                usage_cost=result['cost'],
            )

            # --------------------------- SAVE GENERATED TITLES TO DATABASE ---------------------------
            article_bulk_create = []
            existing_titles = self._user.articles.filter(website=self._website).values_list("title", flat=True).distinct()                    
            for title_data in results:
                title: str = unescape_amp_char(title_data['title'].strip())
                title_inspired_from = None

                for result in serper_results:
                    if title_data['inspired_by_title'] == result['title'].strip():
                        title_inspired_from = result['url']
                        break

                if title in list_of_titles or (
                    type_of_generation == "default"
                    and title in existing_titles
                ):
                    continue

                if title_inspired_from is None:
                    article_bulk_create.append(Article(
                        article_uid=generate_article_uid(self._user.username),
                        website=self._website,
                        title=title,
                        keyword=keyword_object,
                        other_top_ranking_urls=other_top_ranking_urls,
                    ))

                else:
                    other_top_ranking_urls_count = len(other_top_ranking_urls)
                    top_2_ranking_urls = other_top_ranking_urls[:2]

                    if title_inspired_from in top_2_ranking_urls:
                        top_2_ranking_urls.append(other_top_ranking_urls[-1])
                        top_2_ranking_urls.pop(top_2_ranking_urls.index(title_inspired_from))

                    if other_top_ranking_urls_count == 1:
                        article_bulk_create.append(Article(
                            article_uid=generate_article_uid(self._user.username),
                            website=self._website,
                            title=title,
                            keyword=keyword_object,
                            other_top_ranking_urls=[title_inspired_from]
                        ))

                    elif other_top_ranking_urls == 2:
                        article_bulk_create.append(Article(
                            article_uid=generate_article_uid(self._user.username),
                            website=self._website,
                            title=title,
                            keyword=keyword_object,
                            other_top_ranking_urls=[top_2_ranking_urls[0], title_inspired_from]
                        ))

                    else:
                        article_bulk_create.append(Article(
                            article_uid=generate_article_uid(self._user.username),
                            website=self._website,
                            title=title,
                            keyword=keyword_object,
                            other_top_ranking_urls=[top_2_ranking_urls[0], top_2_ranking_urls[1], title_inspired_from]
                        ))

                list_of_titles.append(title)

            # Bulk save the new articles
            Article.objects.bulk_create(article_bulk_create)

            self._user.save()

            # send back titles
            return list_of_titles

        except Exception as e:
            logger.error(f"Error in generating titles: {e}")
            return "Error in generating titles: " + str(e)

    # Fetching Serp Titles
    @transaction.atomic
    def fetch_titles_serper(self, keyword: str, location: str = "us", max_titles: int = 10, type_of_generation: str = "default") -> List[str]:
        """
        Fetch titles from Serper based on a keyword.
        """
        try:
            # Fetch the top articles from Serper
            serper_results: List[str] = generate_titles_from_serper(keyword, location, max_titles)
            logger.debug(f"Serper Results: {serper_results}")  # Log Serper results

            # Ensure serper_results is a list of strings
            if not isinstance(serper_results, list):
                raise ValueError("Expected a list from Serper, got: {}".format(type(serper_results)))

            logger.debug(f"> Prepping title generation with Keyword: {keyword}")

            # Prepare example title sentences directly from strings
            inputs = [{
                "keyword": keyword,
                "example_title_sentences": '\n\n'.join(f'"{title}"' for title in serper_results),
                "language": self._website.article_language_preference,
            }]

            self.title_gen_input_variables = ["keyword", "example_title_sentences", "language"]
            
            # Generate titles synchronously
            result = asyncio.run(self._generate_titles_async(inputs)) 
            
            logger.debug(f"Title Generation Result: {result}")  

            # Check if the result has the expected structure
            if 'results' not in result or not isinstance(result['results'], list):
                raise ValueError("Invalid results format returned from title generation.")

            logger.debug(f"[*] Article Title Generation: Generated {len(result['results'])} titles")

            # Return the generated titles to highlight directly on the page
            return [title_data['title'] for title_data in result['results']]

        except Exception as e:
            logger.error(f"Error in fetching titles: {e}")
            return []

