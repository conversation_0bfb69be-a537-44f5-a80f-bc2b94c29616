import os
import json
import re
import datetime
import logging
import itertools
from urllib.parse import urlparse
from typing import Dict, List, Tuple
from concurrent.futures import ThreadPoolExecutor

import requests
from bs4 import BeautifulSoup

logger = logging.getLogger('abun.title_generation')

# Domains to ignore
IGNORE_DOMAINS = [
    "reddit.com",
    "www.reddit.com",
    "quora.com",
    "www.quora.com",
    "pinterest.com",
    "www.pinterest.com",
    "youtube.com",
    "www.youtube.com",
    "twitter.com",
    "www.twitter.com",
    "en.wikipedia.org",
    "linkedin.com",
    "www.linkedin.com",
    "instagram.com",
    "www.instagram.com",
    "askubuntu.com",
    "www.askubuntu.com",
    "stackoverflow.com",
    "www.stackoverflow.com",
    "stackexchange.com",
    "www.stackexchange.com"
]

# Keywords to identify blog/article url
DOMAIN_SLUG = ["article", "blog"]


def check_publish_date(soup):
    date_patterns = [
        re.compile(r'\d{4}-\d{2}-\d{2}'),
        re.compile(r'\d{2}/\d{2}/\d{4}'),
        re.compile(r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2},\s+\d{4}\b')
    ]
    
    for tag in soup.find_all(['time', 'span', 'p']):
        if any(pattern.search(tag.text) for pattern in date_patterns):
            return True, "Publish date found"
    return False, "No clear publish date detected"

def check_author_name_script(soup):
    # Find JSON-LD script tag
    json_ld_script = soup.find("script", type="application/ld+json")
    if not json_ld_script:
        return False, None

    # Parse JSON data
    try:
        json_ld_data = json.loads(json_ld_script.string)
    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing error: {e}")
        return False, None

    # Handle list format
    if isinstance(json_ld_data, list):
        json_ld_data = json_ld_data[0]

    # Check direct author field
    author = json_ld_data.get("author")
    if author:
        # Handle dictionary author
        if isinstance(author, dict) and "name" in author:
            return True, author["name"]
        # Handle list of authors
        if isinstance(author, list):
            for author_item in author:
                if "name" in author_item:
                    return True, author_item["name"]

    # Check @graph field
    graph_data = json_ld_data.get("@graph", [])
    for item in graph_data:
        # Check for author in graph item
        if "author" in item and "name" in item["author"]:
            return True, item["author"]["name"]
        # Check for Person type
        if "@type" in item and "Person" in item["@type"] and "name" in item:
            return True, item["name"]

    return False, None

def check_author(soup):
    author_patterns = ['author', 'byline', 'writer', 'posted-by', 'contributor', 'author-name', 'post-author']
    result = check_author_name_script(soup)
    if result is None:
        is_valid = False
        author_name = "No data"
    else:
        is_valid, author_name = result
    if is_valid:
        return is_valid, author_name
    for pattern in author_patterns:
        author_tag = soup.find(class_=re.compile(pattern, re.I))
        if author_tag:
            author_text = author_tag.get_text(strip=True)
            return True, author_text
        else:
            return is_valid, author_name
    return False, "No clear author information detected"


def check_blog_link(blog_url: str) -> Tuple:
    """
    Check if the given URL is an article or not.

    Args:
        - blog_url (str): The URL of the blog to be checked.

    Returns:
        - Tuple: True if the URL is an article, False otherwise & Author details.
    """
    headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    author_details = "NO data"
    # Checking if the domain is in IGNORE_DOMAINS and if the url only contains domain
    split_link = blog_url.split("/")
    if (split_link[2] in IGNORE_DOMAINS) or len(split_link) <= 3:
        return False, author_details 

    # Checking if the url is a blog link
    if len(split_link) == 4:
        if split_link[3] == "":
            return False, author_details # Skip if the url is just the domain name
        
    try:
        # Send a GET request to the blog URL with a timeout of 15 seconds
        try:
            response = requests.get(url=blog_url, headers=headers, timeout=15)
        except requests.exceptions.Timeout:
            return False, author_details

        # Check if the response status code is 200
        if response.status_code == 200:
            # Parse the HTML content of the response
            soup = BeautifulSoup(response.content, "html.parser")
            # check for author information
            try: 
                author_found, author_details = check_author(soup)
                if author_found:
                    return True, author_details
            except Exception as e:
                logger.error(e)
            # Checking if the domain is in DOMAIN_SLUG
            for slug in DOMAIN_SLUG:
                if slug in blog_url:
                    return True, author_details

            # check url structure: Check if '/article/', '/news/', '/blog/' is in the url
            if "/article/" in blog_url or "/news/" in blog_url or "/blog/" in blog_url:
                return True, author_details
            
            # Find the 'article' tag in the HTML content
            article_tag = soup.find("article")
            if article_tag:
                return True, author_details
    
            # Like we are checking for <article> tag we need to check all <div> tag and see if their class name or id name is 'article',
            # if it is then we can say that the page is an article page
            div_tags = soup.find_all("div")
            for div_tag in div_tags:
                if div_tag.get("class") == "article" or div_tag.get("id") == "article":
                    return True, author_details
            
            # check for publish date
            publish_date_found, _ = check_publish_date(soup)
            if publish_date_found:
                return True, author_details

            # Extract the 'og:type' meta tag content if present, otherwise set it to an empty string
            try:
                og_type_tag = soup.find("meta", property="og:type", content="article")
                og_content_text = og_type_tag["content"].lower() if og_type_tag else ""
            except:
                og_content_text = ""
            
            # Extract the '@type' attribute from the 'application/ld+json' script tag if present, otherwise set it to an empty string
            try:
                schema_type_tag = soup.find("script", type="application/ld+json")
                schema_content_text = (
                    json.loads(schema_type_tag.string).get("@type", "").lower()
                    if schema_type_tag
                    else ""
                )
            except:
                schema_content_text = ""

            # Check if the blog URL points to an article based on 'og:type' and '@type'
            return ("article" in og_content_text) or ("article" in schema_content_text), author_details

    except Exception as err:
        logger.error(f"Error occurred while checking blog link: {err}")
        return False, author_details


def fetch_hypestat_data(domain: str):
    """
    Fetches HypeStat data for a given domain.

    Args:
        domain (str): The domain for which to fetch the data.

    Returns:
        dict: A dictionary containing the fetched HypeStat data. The dictionary has the following keys:
            - domain_authority: The domain authority of the domain.
    """
    res = requests.get(
        f"https://wjoazptcqycbxodkxmgkgr2yb40qrfpz.lambda-url.us-east-1.on.aws/?domain={domain}"
    )
    if res.status_code == 200:
        res_json = res.json()
        return {
            # "domain": domain,
            # "organic_traffic": res_json["organic_traffic"],
            # "organic_keywords": res_json["organic_keywords"],
            "domain_authority": res_json["domain_authority"],
            # "total_backlinks": res_json["total_backlinks"],
            # "follow": res_json["follow"],
            # "no_follow": res_json["no_follow"],
            # "referring_domains": res_json["referring_domains"],
        }
    else:
        logger.error(f"[*] Could not fetch hypestat data for {res.status_code}")
        return {
            # "domain": domain,
            # "organic_traffic": None,
            # "organic_keywords": None,
            "domain_authority": None,
            # "total_backlinks": None,
            # "follow": None,
            # "no_follow": None,
            # "referring_domains": None,
        }


def extract_blog_data(blog_url: str, keyword: str) -> Dict:
    """
    Extracts relevant data from a blog URL.

    Args:
        blog_url (str): The URL of the blog.
        keyword (str): The keyword to search for in the blog content.

    Returns:
        dict: A dictionary containing the extracted data with the following keys:
            - title (str): The title of the blog.
            - description (str): The meta description of the blog.
            - word_count (int): The word count of the blog content.
            - internal_links (int): The number of internal links in the blog.
            - external_links (int): The number of external links in the blog.
            - images (int): The number of images in the blog.
            - last_updated (str): The last updated/published date of the blog.
            - keyword_count (int): The number of times the keyword appears in the blog content.
    """
    try:
        # Send a GET request to the blog URL
        try:
            response = requests.get(blog_url, timeout=15)
        except requests.exceptions.Timeout:
            logger.error(f"Failed to extract blog data for '{blog_url}' blog.")
            return {}

        # If the request is successful
        if response.status_code == 200:

            # Parse the HTML content of the blog
            soup = BeautifulSoup(response.content, "html.parser")

            # parsing the title and description from the HTML co

            # Find and extract the 'og:title' meta tag from the HTML
            og_title_tag = soup.find("meta", property="og:title", content="article")
            og_title_text = og_title_tag["content"] if og_title_tag else ""

            # Find and extract the meta description tag from the HTML
            meta_description = soup.find("meta", attrs={"name": "description"})
            description = meta_description["content"] if meta_description else ""

            # If the og title is not present, use the title tag from the HTML
            if og_title_text == "":
                title = soup.find("title").get_text()
            else:
                title = og_title_text
                
            #don't take the title text after | & - from serper
            #truncate serper results titles after "dash (-)" and "|"
            if "|" in title:
                title = title.split("|")[0]
            if "-" in title:
                title = title.split("-")[0]
            if "–" in title:
                title = title.split("–")[0]
            if "—" in title:
                title = title.split("—")[0]

            # Count the number of internal and external links
            domain = urlparse(blog_url).netloc
            links = soup.find_all("a", href=True)
            internal_links = sum(
                1 for link in links if urlparse(link["href"]).netloc == domain
            )
            external_links = len(links) - internal_links

            # Count the number of images
            images = len(soup.find_all("img"))

            # Get the last updated/published date
            date_tag = soup.find("meta", property="article:published_time")
            date_content = date_tag["content"] if date_tag else None

            last_updated = "N/A"
            if date_content:
                try:
                    last_updated = datetime.datetime.strptime(
                        date_content, "%Y-%m-%dT%H:%M:%S+00:00"
                    ).strftime("%A, %B %d, %Y at %H:%M:%S")
                except ValueError:
                    try:
                        last_updated = datetime.datetime.strptime(
                            date_content, "%Y-%m-%dT%H:%M:%S.000Z"
                        ).strftime("%A, %B %d, %Y at %H:%M:%S")
                    except ValueError:
                        try:
                            last_updated = datetime.datetime.strptime(
                                date_content, "%Y-%m-%dT%H:%M:%S"
                            ).strftime("%A, %B %d, %Y at %H:%M:%S")
                        except ValueError:
                            last_updated = date_content

            # Count the number of times the keyword appears in the text
            text = soup.get_text()
            keyword_count = len(re.findall(keyword, text, re.IGNORECASE))
            
            # Extract the accurate word count of the blog content only by finding text in all the paragraphs and headings in the blog content
            all_paragraphs_text = " ".join(paragraph.get_text() for paragraph in soup.find_all("p"))
            all_headings_text = " ".join(heading.get_text() for heading in soup.find_all(["h1", "h2", "h3", "h4", "h5", "h6"]))

            all_text = all_paragraphs_text + all_headings_text

            # Count the number of words in the blog content
            word_count = len(all_text.split())

            return {
                "title": title,
                "description": description,
                "word_count": word_count,
                "internal_links": internal_links,
                "external_links": external_links,
                "images": images,
                "last_updated": last_updated,
                "keyword_count": keyword_count,
            }

    except Exception as err:
        logger.error(f"Error occurred while extracting blog data: {err}")
        return None


def extract_blog_and_fetch_hypestat_data(blog_url: str, keyword: str) -> Tuple | None:
    """
    Extracts relevant data from a blog URL & fetches hypestat data.

    Args:
        blog_url (str): The URL of the blog.
        keyword (str): The keyword to search for in the blog content.

    Returns:
        tuple: A tuple containing extracted blog data & hypestat data.
    """
    blog_data = extract_blog_data(blog_url, keyword)

    if not blog_data:
        return None

    hypestat_data = fetch_hypestat_data(urlparse(blog_url).netloc)

    return (blog_data, hypestat_data)


def find_article_links(keyword: str, location: str, number_of_article: int = 20, max_article_count: int = 20) -> List:
    """
    Fetches article links from Google search results for the given keyword.

    Args:
    - keyword (str): The keyword to search for.
    - location (str): Location for seraching the keyword.
    - number_of_article (int): The total number of article links to find.
    - max_article_count (int): The maximum number of links to scrape to determine if it is an article or not.

    Returns:
    - list of dict: A list of dictionaries containing the rank, url, title, and description of the article links.
    """

    # Construct payload for the Google search API
    payload = json.dumps({"q": keyword, "num": max_article_count, "gl": location})

    # Define the API endpoint and headers
    url = "https://google.serper.dev/search"
    headers = {
        "X-API-KEY": os.environ["GOOGLE_SERPER_API_KEY"],
        "Content-Type": "application/json",
    }

    # Make a POST request to the API endpoint
    logger.info(f"Fetching results from serper for keyword: {keyword}")

    try:
        response = requests.request("POST", url, headers=headers, data=payload, timeout=15)
    except requests.exceptions.Timeout:
        logger.critical(f"Failed to get the response from serper API for '{keyword}' keyword.")
        return []

    data = response.json()

    # Filter article links from the search results
    blog_links = []
    blog_ranks = []

    logger.info("Filtering article links from the serper search results...")

    all_links = [item["link"] for item in data["organic"]]
    all_positions = [item["position"] for item in data["organic"]]

    executor = ThreadPoolExecutor(max_workers=min(len(all_links), 32))

    for idx, result in enumerate(executor.map(check_blog_link, all_links)):

        try:
            is_blog_link, _ = result
        except TypeError:
            is_blog_link = False

        if is_blog_link:
            blog_links.append(all_links[idx])
            blog_ranks.append(all_positions[idx])

    # Extract title, description, and word count from the blog links
    url_with_text = []

    # Create executor
    executor = ThreadPoolExecutor(max_workers=min(len(blog_links), 32))

    logger.info("Running extract_blog_and_fetch_hypestat_data function...")
    for idx, data in enumerate(executor.map(extract_blog_and_fetch_hypestat_data, blog_links, itertools.repeat(keyword))):
        if data:
            blog_data, hypestat_data = data

            if blog_data:
                title = blog_data["title"].encode().decode("utf-8")
                description = blog_data["description"].encode().decode("utf-8")
                da_score = hypestat_data["domain_authority"] if hypestat_data["domain_authority"] is not None else 0
                url_with_text.append(
                    {
                        "rank": blog_ranks[idx],
                        "url": blog_links[idx],
                        "word_count": blog_data["word_count"],
                        "internal_links": blog_data["internal_links"],
                        "external_links": blog_data["external_links"],
                        "images": blog_data["images"],
                        "last_updated": blog_data["last_updated"],
                        "keyword_count": blog_data["keyword_count"],
                        "DA_score": da_score,
                        "title": title,
                        "description": description,
                    }
                )

        logger.info(f"Rank {blog_ranks[idx]}: {blog_links[idx]}")

    # Save the article data to a JSON file
    # json_file_name = f"{keyword.replace(' ', '-')}.json"  # Output File Name

    # with open(json_file_name, 'w') as json_file:
    #     json.dump(url_with_text, json_file, indent=4)

    # print('Article Data Saved in -- ', json_file_name)
    # sort the list of dictionaries by DA score and return
    url_with_text = sorted(url_with_text, key=lambda x: x["DA_score"], reverse=True)

    return url_with_text


def check_domain_to_ignore(blog_url: str) -> bool:
    """
    Check if the given URL belongs to a domain that should be ignored.
    
    Args:
        blog_url (str): The URL of the blog to be checked.
    
    Returns:
        bool: True if the domain should be ignored, False otherwise.
    """
    split_link = blog_url.split("/")
    
    # Checking if the domain is in IGNORE_DOMAINS
    if len(split_link) > 2 and split_link[2] in IGNORE_DOMAINS:
        return True
    return False


# Generate Titles From Serper
def generate_titles_from_serper(keyword: str, location:str= "global", max_titles: int = 10, max_article_count: int = 20) -> List[dict]:
    """
    Generate blog articles using Serper API based on the provided keyword.

    Args:
        keyword (str): The keyword to search for.
        location (str): Location for searching the keyword.
        max_titles (int): Maximum number of blog articles to return.
        max_article_count (int): Maximum number of articles to scrape and filter.

    Returns:
        list of dict: A list of dictionaries containing blog article details such as rank, URL, title, and description.
    """
    # Serper API payload
    payload = json.dumps({"q": keyword, "num": max_article_count, "gl": location})

    # Define the Serper API endpoint and headers
    url = "https://google.serper.dev/search"
    headers = {
        "X-API-KEY": os.environ["GOOGLE_SERPER_API_KEY"],
        "Content-Type": "application/json",
    }

    # Send POST request to Serper API
    try:
        response = requests.post(url, headers=headers, data=payload, timeout=15)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to fetch blogs from Serper: {e}")
        return []

    # Parse the JSON response from Serper
    data = response.json()

    # Extract blog details from the search results
    blog_articles = []
    if "organic" in data:
        for result in data["organic"]:
            link = result.get("link", "")
            title = result.get("title", "").strip()
            description = result.get("snippet", "").strip()  # Use snippet as the description
            rank = result.get("position", 0)  # Get the position of the result

            # Skip links from ignored domains
            if check_domain_to_ignore(link):
                continue

            # Truncate titles if they contain "|", "-", "–", or "—"
            if "|" in title:
                title = title.split("|")[0].strip()
            if "-" in title:
                title = title.split("-")[0].strip()
            if "–" in title:
                title = title.split("–")[0].strip()
            if "—" in title:
                title = title.split("—")[0].strip()

            # Append blog article details
            blog_articles.append({
                "rank": rank,
                "url": link,
                "title": title,
                "description": description,
            })

    # Sort the blog articles by rank and limit to the maximum number of titles
    blog_articles = sorted(blog_articles, key=lambda x: x["rank"])[:max_titles]

    return blog_articles

