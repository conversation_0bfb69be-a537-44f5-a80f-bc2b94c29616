def title_generation_prompt_text():
    return """
Generate some long-tail blog titles for the following keyword: "{keyword}" in {language}.

Example Titles in {language}:
"{example_title_sentences}"

The above example titles are the top ranking search results for the keyword "{keyword}".

Do permutation & combination with parts of the sentence and write new title sentences in {language}, ensuring that they follow the same pattern but with slight modifications to avoid plagiarism.

**Important Instructions:**
- The output titles must be in {language}. Do not use English if {language} is different.
- When generating a title that references a specific year, always use the current year (2025) instead of past years.
- Do not add newline characters ('\n') at the start and end of the JSON output.
- The Title should have a maximum of 65 characters.

{format_instructions}
"""
