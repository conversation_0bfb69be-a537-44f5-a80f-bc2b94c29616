# ================================================================================
# ############################# NOT IN USE CURRENTLY #############################
# ================================================================================

from typing import List

import requests
import re
import html
from bs4 import BeautifulSoup
from unidecode import unidecode


class LandingPageScraperError(Exception):
    def __init__(self, msg: str):
        super(LandingPageScraperError, self).__init__(f"Landingpage Scraper failed: {msg}")


def clean_text(text: str):
    text = re.sub(r'\n|\'|\"', "", text).strip()
    text = html.escape(text)
    text = unidecode(text)
    return text


def text_filter(text: str):
    return all([len(text.strip().split(" ")) > 1])


def scrape_text(domain: str) -> List[str]:
    res = requests.get("https://" + domain, timeout=10)
    if res.status_code == 200:
        soup = BeautifulSoup(res.text, features='html.parser')
        elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'p'])
        texts: List[str] = []
        for element in elements:
            texts.append(clean_text(element.text))

        texts = list(filter(text_filter, texts))

        return texts

    else:
        raise LandingPageScraperError(f"Request to {domain} failed with status code: {res.status_code}")