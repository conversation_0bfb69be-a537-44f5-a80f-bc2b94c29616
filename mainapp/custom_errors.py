# =======================================================================================
# --------------------------- ADD YOUR CUSTOM EXCEPTIONS HERE ---------------------------
# =======================================================================================
from typing import Literal

from mainapp.models import Website


class WebsiteDomainExtractionError(Exception):
    def __init__(self):
        super(WebsiteDomainExtractionError, self).__init__("Failed to extract domain & protocol becasue required"
                                                           " info was missing in header META or the value was empty")



class KeywordsEverywhereNoData(Exception):
    """
    For scenarios where KE API returns []
    """
    def __init__(self, domain: str):
        super(KeywordsEverywhereNoData, self).__init__(
            f"KeywordsEverywhere API returned empty data in response for {domain}"
        )


class KeywordsEverywhereJSONError(Exception):
    """
    For scenarios where KE API returns []
    """
    def __init__(self, domain: str):
        super(KeywordsEverywhereJSONError, self).__init__(
            f"Could not JSON decode KeywordsEverywhere API response for {domain}"
        )
