import os
import time
import logging
import secrets
from typing import Dict, Iterable, List, Any
from concurrent.futures import Thr<PERSON>PoolExecutor

import chromadb
from pydantic import BaseModel
from langchain_chroma import Chroma
from chromadb.config import Settings
from huggingface_hub import InferenceClient
from langchain_openai import OpenAIEmbeddings
from langchain_core.embeddings import Embeddings

from AbunDRFBackend.settings import BASE_DIR, DEBUG
from mainapp.models import WebPage

# Set up logger
logger = logging.getLogger(__name__)


class CustomHuggingFaceInferenceAPIEmbeddings(BaseModel, Embeddings):
    def embed_documents(self, texts):
        client = InferenceClient(provider="hf-inference", api_key=os.environ['HF_API_KEY'])
        text_embeddings = []

        def process_text(text):
            response = client.feature_extraction(
                text=text,
                model="sentence-transformers/all-MiniLM-L6-v2",
            )
            return response.tolist()

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=100) as executor:
            for embeddings in executor.map(process_text, texts):
                text_embeddings.append(embeddings)

        return text_embeddings

    def embed_query(self, text):
        return self.embed_documents([text])[0]


class ChromaDBManager:
    def __init__(self):
        # Initialize ChromaDB with persistence
        self.persist_dir = os.getenv("CHROMADB_PERSIST_DIR", str(BASE_DIR / "chroma_db"))

        # Connect to chroma client
        self.client = chromadb.HttpClient(
            host=os.environ['CHROMADB_HOST'],
            port=os.environ['CHROMADB_PORT']
        )

        # Initialize embedding function based on environment
        if not DEBUG:
            # Use openai embeddings for production
            self.embedding_function = OpenAIEmbeddings(
                model="text-embedding-3-small",
                openai_api_key=os.environ['TEXT_EMBEDDING_OPENAI_API_KEY']
            )

        else:
            # Use huggingface embeddings for development
            self.embedding_function = CustomHuggingFaceInferenceAPIEmbeddings(
                model_name="sentence-transformers/all-MiniLM-L6-v2",
                api_key=os.environ['HF_API_KEY']
            )

        # Dictionary to cache vectorstores by website_id
        self._vectorstore_cache = {}

    def get_collection_name(self, website_id: int, collection_type: str = "content") -> str:
        """
        Generate a collection name for a specific website and collection type

        :param website_id: The ID of the website
        :param collection_type: The type of collection ("content" for title+summary or "urls" for URLs)
        :return: The collection name
        """
        if collection_type == "urls":
            return f"website_{website_id}_urls"
        else:
            return f"website_{website_id}_pages"

    def get_vectorstore(self, website_id: int, collection_type: str = "content") -> Chroma:
        """
        Get or create a vectorstore for a specific website and collection type

        :param website_id: The ID of the website
        :param collection_type: The type of collection ("content" for title+summary or "urls" for URLs)
        :return: The vectorstore for the specified collection
        """
        # Create a cache key that includes both website_id and collection_type
        cache_key = f"{website_id}_{collection_type}"

        # Check if vectorstore is already in cache
        if cache_key in self._vectorstore_cache:
            return self._vectorstore_cache[cache_key]

        collection_name = self.get_collection_name(website_id, collection_type)

        # Create new vectorstore
        vectorstore = Chroma(
            client=self.client,
            embedding_function=self.embedding_function,
            collection_name=collection_name,
            client_settings=Settings(anonymized_telemetry=False)
        )

        # Cache the vectorstore
        self._vectorstore_cache[cache_key] = vectorstore
        return vectorstore

    def add_page(self, webpage: WebPage) -> str | None:
        """
        Add a webpage to the vector database.
        :return: Returns the ChromaDB ID for the webpage.
        """
        try:
            # Generate a unique ID for the document
            chroma_id = f"page_{webpage.id}_{secrets.token_hex(8)}"

            # Add to content collection (title + summary)
            self._add_to_collection(
                webpage=webpage,
                chroma_id=chroma_id,
                collection_type="content",
                document=f"{webpage.title}\n\n{webpage.summary}"
            )

            # Add to URLs collection
            self._add_to_collection(
                webpage=webpage,
                chroma_id=chroma_id,
                collection_type="urls",
                document=webpage.url
            )

            return chroma_id

        except Exception as e:
            logger.error(f"Error adding page {webpage.url} to ChromaDB after multiple retries: {str(e)}", exc_info=True)
            return None

    def _add_to_collection(self, webpage: WebPage, chroma_id: str, collection_type: str, document: str) -> None:
        """
        Helper method to add a document to a specific collection with retry logic

        :param webpage: The WebPage object
        :param chroma_id: The ChromaDB ID to use
        :param collection_type: The type of collection ("content" or "urls")
        :param document: The document text to add
        """
        # Get the vectorstore for this website and collection type
        vectorstore = self.get_vectorstore(webpage.website_id, collection_type)

        # Add the document to Chroma with retry logic
        max_retries = 5
        retry_count = 0
        backoff_time = 1

        while retry_count < max_retries:
            try:
                vectorstore.add_texts(
                    texts=[document],
                    metadatas=[{
                        "website_id": webpage.website_id,
                        "url": webpage.url,
                        "title": webpage.title,
                        "updated_on": webpage.last_modified_on and webpage.last_modified_on.isoformat() or "Not Found"
                    }],
                    ids=[chroma_id]
                )
                logger.info(f"Successfully added page {webpage.url} to ChromaDB {collection_type} collection for website {webpage.website_id} with ID {chroma_id}")
                return

            except Exception as e:
                retry_count += 1

                if retry_count >= max_retries:
                    raise

                logger.warning(f"ChromaDB connection error on attempt {retry_count}/{max_retries}: {str(e)}. Retrying in {backoff_time}s...")

                time.sleep(backoff_time)
                backoff_time *= 2  # Exponential backoff

    def find_similar_pages(self, query: str, website_id: int,
                          num_results: int = 5) -> List[Dict[str, Any]]:
        """
        Find similar pages for a given query within a specific website.
        Searches both content (title + summary) and URLs collections and combines the results.
        :return: Returns pages with combined similarity scores, sorted by highest score
        """
        try:
            # Search in content collection (title + summary)
            content_results = self._search_collection(
                query=query,
                website_id=website_id,
                collection_type="content",
                num_results=num_results,
                score_threshold=0.4
            )

            # Search in URLs collection
            url_results = self._search_collection(
                query=query,
                website_id=website_id,
                collection_type="urls",
                num_results=num_results,
                score_threshold=0.4
            )

            # Combine and process results
            return self._combine_search_results(content_results, url_results, query, num_results)

        except Exception as e:
            logger.error(f"Error querying ChromaDB for website_id {website_id} after multiple retries: {str(e)}", exc_info=True)
            return []

    def _search_collection(self, query: str, website_id: int, collection_type: str,
                          num_results: int = 5, score_threshold: float = 0.4) -> List[Dict[str, Any]]:
        """
        Helper method to search a specific collection

        :param query: The search query
        :param website_id: The website ID
        :param collection_type: The type of collection ("content" or "urls")
        :param num_results: Maximum number of results to return
        :param score_threshold: Minimum similarity score threshold
        :return: List of search results with similarity scores
        """
        try:
            # Get the vectorstore for this website and collection type
            excluded_urls = set(
                WebPage.objects.filter(website_id=website_id, include_linking="off").values_list("url", flat=True)
            )
            
            vectorstore = self.get_vectorstore(website_id, collection_type)

            # Retry logic for similarity search
            max_retries = 3
            retry_count = 0
            backoff_time = 1

            while retry_count < max_retries:
                try:
                    results = vectorstore.similarity_search_with_relevance_scores(
                        query,
                        k=num_results,
                        score_threshold=score_threshold
                    )

                    similar_pages = []
                    for doc, similarity_score in results:
                        if metadata["url"] in excluded_urls:
                            continue                        
                        metadata = doc.metadata
                        result = {
                            "url": metadata["url"],
                            "title": metadata["title"],
                            "query": query,
                            "similarity": round(similarity_score, 3)
                        }

                        # Add summary for content collection
                        if collection_type == "content":
                            result["summary"] = doc.page_content.split("\n\n")[1] if "\n\n" in doc.page_content else doc.page_content

                        similar_pages.append(result)

                    logger.debug(f"Found {len(similar_pages)} similar pages in {collection_type} collection for website_id {website_id}")
                    return similar_pages

                except Exception as e:
                    retry_count += 1

                    if retry_count >= max_retries:
                        raise

                    logger.warning(f"ChromaDB search error on attempt {retry_count}/{max_retries}: {str(e)}. Retrying in {backoff_time}s...")

                    time.sleep(backoff_time)
                    backoff_time *= 2  # Exponential backoff

        except Exception as e:
            logger.error(f"Error querying ChromaDB {collection_type} collection for website_id {website_id}: {str(e)}", exc_info=True)
            return []

    def _combine_search_results(self, content_results: List[Dict[str, Any]],
                               url_results: List[Dict[str, Any]],
                               query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """
        Combine search results from content and URL collections

        :param content_results: Results from content collection
        :param url_results: Results from URLs collection
        :param query: The original search query
        :param num_results: Maximum number of results to return
        :return: Combined and sorted results
        """
        # Create a mapping of URLs to their content results
        content_map = {result["url"]: result for result in content_results}

        # Create a mapping of URLs to their URL results
        url_map = {result["url"]: result for result in url_results}

        # Get all unique URLs from both result sets
        all_urls = set(content_map.keys()) | set(url_map.keys())

        # Combine results
        combined_results = []

        for url in all_urls:
            content_score = content_map.get(url, {}).get("similarity", 0)
            url_score = url_map.get(url, {}).get("similarity", 0)

            # If we have both scores, use the sum
            if url in content_map and url in url_map:
                combined_score = content_score + url_score
                result = content_map[url].copy()  # Use content result as base since it has summary
                result["similarity"] = round(combined_score, 3)
                combined_results.append(result)
            # If we only have content score
            elif url in content_map:
                combined_results.append(content_map[url])
            # If we only have URL score and it's above threshold
            elif url in url_map and url_score >= 0.4:
                # For URL-only results, we need to ensure they have all required fields
                result = url_map[url].copy()
                # Add a placeholder summary if needed
                if "summary" not in result:
                    result["summary"] = ""
                combined_results.append(result)

        # Sort by similarity score (highest first)
        combined_results.sort(key=lambda x: x["similarity"], reverse=True)

        # Limit to top results
        return combined_results[:num_results]

    def delete_pages(self, embedding_ids: str | List, website_id: int) -> None:
        """
        Delete webpages from the vector database.
        """
        try:
            if isinstance(embedding_ids, str):
                embedding_ids = [embedding_ids]

            # Delete from content collection
            self._delete_from_collection(embedding_ids, website_id, "content")

            # Delete from URLs collection
            self._delete_from_collection(embedding_ids, website_id, "urls")

            logger.info(f"Completed deletion process for {len(embedding_ids)} pages from all ChromaDB collections for website {website_id}")

        except Exception as e:
            logger.error(f"Error deleting pages from ChromaDB: {str(e)}", exc_info=True)

    def _delete_from_collection(self, embedding_ids: List[str], website_id: int, collection_type: str) -> None:
        """
        Helper method to delete documents from a specific collection

        :param embedding_ids: List of ChromaDB IDs to delete
        :param website_id: The website ID
        :param collection_type: The type of collection ("content" or "urls")
        """
        try:
            # Get the vectorstore for this website and collection type
            vectorstore = self.get_vectorstore(website_id, collection_type)

            # Retry logic for delete operations
            max_retries = 3
            retry_count = 0
            backoff_time = 1

            # Batch size for deletion operations
            batch_size = 50

            # Process deletions in batches
            for i in range(0, len(embedding_ids), batch_size):
                batch_ids = embedding_ids[i:i+batch_size]

                retry_count = 0
                while retry_count < max_retries:
                    try:
                        vectorstore.delete(ids=batch_ids)
                        logger.info(f"Successfully deleted batch {i//batch_size + 1} ({len(batch_ids)} pages) from ChromaDB {collection_type} collection for website {website_id}")
                        break

                    except Exception as e:
                        retry_count += 1

                        if retry_count >= max_retries:
                            logger.error(f"Failed to delete batch {i//batch_size + 1} from {collection_type} collection after {max_retries} attempts: {str(e)}")
                            break

                        logger.warning(f"ChromaDB batch delete error on attempt {retry_count}/{max_retries}: {str(e)}. Retrying in {backoff_time}s...")

                        time.sleep(backoff_time)
                        backoff_time *= 2  # Exponential backoff

        except Exception as e:
            logger.error(f"Error deleting pages from ChromaDB {collection_type} collection: {str(e)}", exc_info=True)

    def bulk_add_pages(self, webpages: Iterable[WebPage]) -> Dict[int, str]:
        """
        Bulk add multiple webpages to ChromaDB.
        """
        try:
            # Group webpages by website_id
            webpages_by_website = {}
            for webpage in webpages:
                if webpage.website_id not in webpages_by_website:
                    webpages_by_website[webpage.website_id] = []
                webpages_by_website[webpage.website_id].append(webpage)

            webpage_map = {}

            # Process each website's pages separately
            for website_id, site_webpages in webpages_by_website.items():
                # Process content collection (title + summary)
                self._bulk_add_to_collection(
                    website_id=website_id,
                    site_webpages=site_webpages,
                    collection_type="content",
                    document_generator=lambda wp: f"{wp.title}\n\n{wp.summary}",
                    webpage_map=webpage_map
                )

                # Process URLs collection
                self._bulk_add_to_collection(
                    website_id=website_id,
                    site_webpages=site_webpages,
                    collection_type="urls",
                    document_generator=lambda wp: wp.url,
                    webpage_map=None  # Don't update the map again
                )

            return webpage_map

        except Exception as e:
            logger.error(f"Error bulk adding pages to ChromaDB: {str(e)}", exc_info=True)
            return {}

    def _bulk_add_to_collection(self, website_id: int, site_webpages: List[WebPage],
                               collection_type: str, document_generator: callable,
                               webpage_map: Dict[int, str] = None) -> None:
        """
        Helper method to bulk add documents to a specific collection

        :param website_id: The website ID
        :param site_webpages: List of WebPage objects for the website
        :param collection_type: The type of collection ("content" or "urls")
        :param document_generator: Function that takes a WebPage and returns the document text
        :param webpage_map: Dictionary to update with webpage ID to chroma ID mapping (only for content collection)
        """
        # Get the vectorstore for this website and collection type
        vectorstore = self.get_vectorstore(website_id, collection_type)

        documents = []
        metadatas = []
        ids = []

        for webpage in site_webpages:
            document = document_generator(webpage)

            # Only generate new IDs for the content collection
            # For URLs collection, reuse the same IDs
            if webpage_map is not None and webpage.id not in webpage_map:
                chroma_id = f"page_{webpage.id}_{secrets.token_hex(8)}"
                webpage_map[webpage.id] = chroma_id
            else:
                chroma_id = webpage_map.get(webpage.id) if webpage_map else f"page_{webpage.id}_{secrets.token_hex(8)}"

            documents.append(document)
            metadatas.append({
                "website_id": webpage.website_id,
                "url": webpage.url,
                "title": webpage.title,
                "updated_on": webpage.last_modified_on and webpage.last_modified_on.isoformat() or "Not Found"
            })
            ids.append(chroma_id)

        # Retry logic for bulk operations
        max_retries = 5
        retry_count = 0
        backoff_time = 1

        # Batch size for bulk operations
        batch_size = 50

        # Process documents in batches
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_meta = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]

            retry_count = 0
            while retry_count < max_retries:
                try:
                    vectorstore.add_texts(
                        texts=batch_docs,
                        metadatas=batch_meta,
                        ids=batch_ids
                    )
                    logger.info(f"Successfully added batch {i//batch_size + 1} ({len(batch_docs)} pages) to ChromaDB {collection_type} collection for website {website_id}")
                    break

                except Exception as e:
                    retry_count += 1

                    if retry_count >= max_retries:
                        raise Exception(f"Failed to add batch {i//batch_size + 1} after {max_retries} attempts: {str(e)}")

                    logger.warning(f"ChromaDB batch add error on attempt {retry_count}/{max_retries}: {str(e)}. Retrying in {backoff_time}s...")

                    time.sleep(backoff_time)
                    backoff_time *= 2  # Exponential backoff

        logger.info(f"Successfully bulk added {len(documents)} pages to ChromaDB {collection_type} collection for website {website_id} in {(len(documents) + batch_size - 1) // batch_size} batches")

    def export_website_embeddings(self, website_id: int) -> List[Dict]:
        """
        Export embeddings for a specific website to be used in k8s/Fly.io machines.
        :param website_id: Website ID
        :return: Returns a dictionary with all the necessary data to initialize a local ChromaDB instance.
        """
        try:
            excluded_urls = set(
            WebPage.objects.filter(website_id=website_id, include_linking="off").values_list("url", flat=True)
            )

            # Export content collection (title + summary)
            content_data = self._export_collection_embeddings(website_id, "content")

            # Export URLs collection
            urls_data = self._export_collection_embeddings(website_id, "urls")

            # Combine the data
            export_data = []

            # Create a mapping of URLs to their embeddings from the URLs collection
            url_embeddings = {item["url"]: item["embedding"] for item in urls_data}

            # Merge the data from both collections
            for content_item in content_data:
                if url in excluded_urls:
                    continue
                
                url = content_item["url"]
                export_item = {
                    "title": content_item["title"],
                    "url": url,
                    "summary": content_item["summary"],
                    "content_embedding": content_item["embedding"],
                }

                # Add URL embedding if available
                if url in url_embeddings:
                    export_item["url_embedding"] = url_embeddings[url]

                export_data.append(export_item)

            logger.info(f"Successfully exported {len(export_data)} combined embeddings for website_id {website_id}")
            return export_data

        except Exception as err:
            logger.critical(f"Error exporting embeddings for website_id {website_id}: {str(err)}", exc_info=True)
            return []

    def _export_collection_embeddings(self, website_id: int, collection_type: str) -> List[Dict]:
        """
        Helper method to export embeddings from a specific collection

        :param website_id: The website ID
        :param collection_type: The type of collection ("content" or "urls")
        :return: List of dictionaries with embedding data
        """
        try:
            # Get the vectorstore for this website and collection type
            vectorstore = self.get_vectorstore(website_id, collection_type)

            # Get all documents for the website
            results = vectorstore._collection.get(
                include=["embeddings", "metadatas", "documents"]
                # No need for where clause since we're using a website-specific collection
            )

            if not results:
                logger.warning(f"No embeddings found for website_id {website_id} in {collection_type} collection")
                return []

            # Format the data for export
            export_data = []

            for embedding, metadata, document in zip(results["embeddings"], results["metadatas"], results["documents"]):
                if collection_type == "content":
                    # For content collection, extract summary from document
                    summary = document.split('\n\n')[1] if '\n\n' in document else document
                    export_data.append({
                        "title": metadata["title"],
                        "url": metadata["url"],
                        "summary": summary,
                        "embedding": embedding.tolist(),
                    })
                else:
                    # For URLs collection, the document is the URL
                    export_data.append({
                        "title": metadata["title"],
                        "url": metadata["url"],
                        "embedding": embedding.tolist(),
                    })

            logger.info(f"Successfully exported {len(results['ids'])} embeddings from {collection_type} collection for website_id {website_id}")
            return export_data

        except Exception as err:
            logger.critical(f"Error exporting embeddings from {collection_type} collection for website_id {website_id}: {str(err)}", exc_info=True)
            return []
