from storages.backends.s3boto3 import S3Boto3Storage

class PublicMediaStorage(S3Boto3Storage):
    default_acl = 'public-read'
    object_parameters = {
        'CacheControl': 'max-age=86400',
        'ContentDisposition': 'inline',
    }

    def get_object_parameters(self, name):
        params = super().get_object_parameters(name)
        # infer ContentType from file extension

        if name.endswith(".png"):
            params["ContentType"] = "image/png"

        elif name.endswith(".webp"):
            params["ContentType"] = "image/webp"

        else:
            params["ContentType"] = "image/jpeg"

        return params
