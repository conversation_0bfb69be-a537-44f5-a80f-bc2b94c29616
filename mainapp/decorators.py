from functools import wraps, lru_cache

from django.http import JsonResponse

from mainapp.json_responses import JsonResponseUnauthorized
from mainapp.models import User

from rest_framework.request import Request


def cors_allow_all(view_func):
    @wraps(view_func)
    def add_cors_headers(request, *args, **kwargs):
        json_response: JsonResponse = view_func(request, *args, **kwargs)
        json_response.headers['Access-Control-Allow-Origin'] = "*"
        return json_response

    return add_cors_headers


def subscription_required(view_func):
    """
    This decorator checks if user has an active subscription by querying for 'subscription_plan' field in User model.
    If the value is None, sends back a 402 response to client side.

    Place this below any authentication checks. Keep it as the last decorator if possible.
    """
    @wraps(view_func)
    def pre_check(request: Request, *args, **kwargs):
        user: User = request.user
        if not user.stripe_subscription_id and not user.has_ltd_plans:
            return JsonResponse(status=402, data={'message': "User has not selected any subscription plan."})
        else:
            return view_func(request, *args, **kwargs)

    return pre_check


def admin_only(view_func):
    """
    This decorator only allows 'admin' users to access the view function / api.
    Keep this below any authentication checks if possible.
    """
    @wraps(view_func)
    def pre_check(request: Request, *args, **kwargs):
        user: User = request.user
        if not user.admin:
            return JsonResponse(status=403, data={'message': "User is not admin."})
        else:
            return view_func(request, *args, **kwargs)

    return pre_check


def dynamic_lru_cache():
    """
    Custom decorator to handle dynamic LRU cache size.
    """
    def decorator(func):
        cache = None

        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal cache
            current_users_count: int = User.objects.count()

            if cache is None or cache.cache_info().maxsize != current_users_count:

                if cache:
                    cache.cache_clear()

                cache = lru_cache(maxsize=current_users_count)(func)

            return cache(*args, **kwargs)

        return wrapper    

    return decorator
