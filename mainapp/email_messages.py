from typing import List

from django.db.models import QuerySet

from AbunDRFBackend.settings import RESET_PASSWORD_LINK_DOMAIN, ADMIN_DOMAIN
from mainapp.models import Article, User


def account_email_verification_email_body(username: str, verification_link: str):
    return f"""Hey {username},<br/>
<br/>
Thanks for signing up with <PERSON><PERSON>.<br/> 
You are almost there to get a boost on your organic traffic.<br/>
<br/>
Verify your account to UNLOCK all the features on Abun for Free.<br/>
<br/>
<a style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none;" href="{verification_link}">Click here to Verify</a>
<br/>
<br/>
If the above button doesn't work, try copying & pasting the following link in your browser:<br/>
<br/>
Here's the link for verification: {verification_link}<br/>
<br/> 
Best Regards,<br/> 
Team Abun<br/>
Abun.com"""


# def content_plan_success_email_body(username: str):
#     return f"""Hi {username},<br/>
# <br/>
# Your content plan is ready & available for your review. You can access it from {RESET_PASSWORD_LINK_DOMAIN}/content-plan <br/>
# <br/>
# Please take a moment to go through it, & if you need further assistance, feel free to reach out to us via live chat.<br/>
# <br/>
# <br/>
# Best Regards,<br/>
# Team Abun<br/>
# Abun.com"""


# def content_plan_failed_email_body(username: str):
#     return f"""Hi {username},<br/>
# <br/>
# I'm really sorry. Your content plan generation seems to have failed.<br/>
# <br/>
# I've tried making it super easy for you to regenerate your content plan again by just clicking on the following link:<br/>
# <br/>
# {RESET_PASSWORD_LINK_DOMAIN}/content-plan <br/>
# <br/>
# If the error persists, please don't hesitate to contact us directly via live chat.<br/>
# <br/>
# Best Regards,<br/> 
# Team Abun<br/>
# Abun.com"""


def reset_password_email_body(username: str, reset_link: str, expiry_hours: int):
    return f"""Hello {username},<br/>
<br/>
We received your request to reset your password.<br/>
<br/>
Your password reset LINK WILL EXPIRE in {expiry_hours} hours:<br/>
<br/>
{reset_link}<br/>
<br/>
If your link has expired, you can send another request to reset your password from here: https://abun.com/forgot-password<br/>
<br/>
Best Regards,<br/>
Team Abun<br/>
Abun.com"""


def article_generation_success_email_body(username: str, article_uid: str):
    return f"""Hi {username},<br/>
<br/>
Your requested article is now ready for review. You can access it by clicking the link below:<br/>
<br/>
{RESET_PASSWORD_LINK_DOMAIN}/articles/edit/{article_uid}/<br/>
<br/>
Note: You can manage these email notifications from your <a href="{RESET_PASSWORD_LINK_DOMAIN}/profile">profile</a>.<br/>
<br/>
We hope you find it valuable. If you have any feedback or need further assistance, please don't hesitate to reach out to us via live chat.<br/>
<br/>
<br/>
Best Regards,<br/>
Team Abun<br/>
Abun.com"""


def article_generation_failed_email_body(username: str):
    return f"""Hi {username},<br/>
<br/>
I'm really sorry. Your article generation seems to have failed.<br/>
<br/>
I've tried making it super easy for you to regenerate your article again by just clicking on the following link:<br/>
<br/>
{RESET_PASSWORD_LINK_DOMAIN}/articles <br/>
<br/>
If the error persists, please don't hesitate to contact us directly via live chat.<br/>
<br/>
Best Regards,<br/> 
Team Abun<br/>
Abun.com"""


def contact_us_email_body(username: str, user_email: str, user_message: str):
    user_message = user_message.replace('\n', '<br/>')
    return f"""Email Message from {username} ({user_email})<br/>
<br/>
-------------------------
<br/>
{user_message}
"""


def contact_us_ack_email_body(username: str):
    return f"""Hi {username},<br/>
<br/>
I'm Junaid, co-founder at Abun.<br/> 
I'd love to help you with your query. Abun is exploding with alot of happy users right now.<br/> 
Just allow me sometime to run through your query in detail & I shall surely try to reach out to you ASAP.<br/>
<br/>
If you have require any immediate assistance, please don't hesitate to contact me directly via live chat.<br/>
<br/>
P.S. I'm curious to know what you feel we should build next; Creating long-form content or Alternative-to type article?<br/>
<br/>
Best Regards,<br/> 
Junaid Ansari<br/>
Co-founder<br/>
Abun.com"""


def ask_for_feedback_email_body(username: str):
    return f"""Hey {username},<br/>
<br/>
Junaid here again 👋<br/>
<br/>
I hope you have checked out Abun.com<br/>
<br/>
Can I ask a favor?<br/>
<br/>
You are like a Shark Tank Judge to me!<br/>
Would love to get some quick feedback from you<br/>
<br/>
<img src="https://res.cloudinary.com/diaiivikl/image/upload/v1702487881/shark_tank_vdy2tu.gif" width="auto" height="auto" alt="Shark Tank Judge" /><br/>
<br/>
I have prepared a short & simple yes/no questionnaire: <a href="https://forms.gle/etyGjkPPg5D6chEW6">https://forms.gle/etyGjkPPg5D6chEW6</a><br/>
<br/>
Excited to check your response!<br/>
<br/>
--<br/>
Best Regards,<br/>
<b>Junaid Ansari</b><br/>
Co-founder<br/>
Abun.com<br/>
<br/>
<b>
    <a href="https://twitter.com/JunaidAnsari" style="color:rgb(17,85,204)" target="_blank">Twitter</a> |
    <a href="https://www.linkedin.com/in/iamjunaidansari/" style="color:rgb(17,85,204)" target="_blank">LinkedIn</a> |
    <a href="https://www.indiehackers.com/JunaidBhai" style="color:rgb(17,85,204)" target="_blank">IndieHackers</a> |
    <a href="https://www.reddit.com/user/JunaidBhai" style="color:rgb(17,85,204)" target="_blank">Reddit</a> |
    <a href="https://www.producthunt.com/@junaidansari" style="color:rgb(17,85,204)" target="_blank">ProductHunt</a>
</b>
"""


def founders_email_body():
    return f"""Hey,<br/>
<br/>
Amin here, co-founder at Abun.com<br/>
<br/>
<b>Marketing is difficult.</b><br/>
<br/>
<img src="https://res.cloudinary.com/diaiivikl/image/upload/v1722527179/emailer/dump-advance-marketing.gif" alt="marketing gif" width="251" height="188"><br/>
<br/>
I have launched 3 startups in the past & <b>getting traffic to your site has always been a task</b>.<br/>
<br/>
For all of my projects, it was <b>organic traffic that was winning the marketing for me</b>.<br/>
I built Abun, so I can replicate it for everyone.<br/>
<br/>
Here is what worked & <b>how you can do it too.</b><br/>
<br/>
I assume that you have the basics right, like.<br/>
- Make sure your website load time is faster<br/>
- Meta Title & Descriptions are setup<br/>
- Zero open issues in Google Search Console.<br/>
<br/>
<b>Now let's get you organic traffic.</b><br/>
You simply need to do these two things. Trust me. Only these 2.<br/>
- Increase your DA Score<br/>
- Consistently write Articles around your niche keywords (preferably long-tail keywords)<br/>
<br/>
To increase your DA Score, use <a href="https://abun.com/category/free-high-quality-backlinks" target="_blank">this list</a> to build free backlinks from high DA/DR websites.<br/>
DA Score doesn't immediately increase, it keeps growing every week or two based on when your backlinks are being indexed.<br/>
Once you have a decent DA score (>15) you will see your website, pages & blog posts improve rankings.<br/>
<br/>
Next thing is simple, Do keyword research > start writing content on your blog around your target keywords.<br/>
Try writing articles for long tail keywords with lesser volume, usually those are easy ones that you can quickly rank for on the top page<br/>
<br/>
You can manually write articles or if you want to <b>automate this entire process,</b><br/>
then you can use <a href="https://abun.com/" target="_blank">Abun.com</a> to<br/>
- keyword research for you<br/>
- <b>create quality articles</b><br/>
- internal link those articles with your other articles/pages.<br/>
- automate the entire process<br/>
<br/>
I am working on building the best AI Tool to Automate SEO and content.<br/>
There are tons of such tools in the market, but<br/>
my focus is to build something that <b>generates REAL organic traffic to your site</b><br/>
<b>without spamming Google or ugly robotic content</b><br/>
<br/>
If you have used Abun & have any suggestions/features, do let me know<br/>
<br/>
Apart from this, I love making friends, so let's connect.<br/>
Tell me more about you, what are you working on?<br/>
<br/>
<div>
<b>Amin Memon</b><br/>
Co-Founder at <a href="https://abun.com/" target="_blank">Abun</a> & <a href="https://draftss.com/" target="_blank">Draftss</a>
</div>
"""


def stuck_process_body_for_article_generation(email: str, process_name: str, logs: List[str]):
    return f"""{email} - '{process_name}' job stuck in processing.
<br/>
{"<br/>".join(logs)}
"""


def stuck_process_body_for_website_scanning(email: str, website_domain: str, process_name: str):
    return f"""{email} - {website_domain} - '{process_name}' job stuck in processing.
<br/>
"""


def website_indexation_process_completed_body(username: str, website_domain: str):
    return f"""Hi {username},<br/>
<br/>
We are pleased to inform you that the indexing process for {website_domain} has been successfully completed.<br/> 
Thank you for your patience during this process.
<br/>
<br/>
Best Regards,<br/>
Team Abun<br/>
Abun.com"""


def unable_to_publish_article_body(username: str):
    return f"""Hey {username},<br/>
<br/>
Our recent attempt at auto-publishing an article to your blog from Abun failed 😕<br/>  
Everything looks good on our end, but some WordPress settings or security rules might be blocking the request.<br/><br/>  
Here’s How to Fix It:<br/><br/>  
<b>1. WordPress Permalink Structure</b><br/>  
Go to WordPress Admin Dashboard → Settings → Permalinks, and set it to “Post Name.”<br/>  
This improves API compatibility and even helps with Google rankings.<br/><br/>  
<b>2. WordPress User Authorization Check</b><br/> 
Your Application Password may have expired or been removed.<br/>  
Go to Users → Profile and check the "Application Password" section.<br/>  
If Abun isn’t listed, reconnect your WordPress account with Abun.<br/><br/>  
<b>3. Cloudflare Blocking Requests</b><br/>  
If your site uses Cloudflare, it might be blocking Abun’s API requests.<br/><br/>  
How to Fix It:<br/>  
- Log in to your Cloudflare Dashboard<br/>  
- Go to Firewall → Tools<br/>  
- Under IP Access Rules, add the following IPs to the Allowlist:<br/>  
&nbsp;&nbsp;&nbsp;&nbsp;*************<br/>  
&nbsp;&nbsp;&nbsp;&nbsp;**********<br/>
&nbsp;&nbsp;&nbsp;&nbsp;***********<br/>
- Go to Security > Bots and turn OFF "<b>Bot Fight Mode</b>"<br/><br/>  
<b>4. Security Plugins Blocking API Requests</b><br/>  
Some security plugins can interfere with auto-publishing. If you're using:<br/>  
- WordFence → Ensure "Disable Application Passwords" is off<br/>  
- Really Simple SSL → Disable “Disable user enumeration” under Settings > Hardening > Basic<br/>  
- LiteSpeed Cache → Remove restrictions on /wp-json<br/><br/>  
Try temporarily disabling security plugins one by one to find the culprit.<br/><br/>  
<b>5. XML-RPC Blocking API Requests</b><br/>  
Some hosting providers and security plugins disable XML-RPC, which can block API access.<br/><br/>  
How to Check If XML-RPC Is Blocked:<br/>  
- Open your browser and go to: https://yourwebsite.com/xmlrpc.php<br/>  
- If you see "XML-RPC server accepts requests," it's working fine.<br/>  
- If you get 403 Forbidden or 404 Not Found, XML-RPC is blocked.<br/><br/>  
How to Fix It:<br/>  
- If you have WordFence, iThemes Security, or Disable XML-RPC plugins, check their settings and enable XML-RPC.<br/>  
- If your hosting provider is blocking it, contact their support and request them to enable XML-RPC access.<br/><br/>  
What Next?<br/>  
Once you’ve checked these settings, try publishing an article from Abun to see if it's working.<br/><br/>  
Still stuck? Please reach out to us via live chat for help!<br/><br/>  
Cheers,<br/>  
The Abun Team 🚀"""

def feature_request_email_body(username: str):
    return f"""Hey {username},<br/>
<br/>
Appreciate you dropping your idea! 🚀<br/>
We've got it and our team will take a look soon.<br/>
Your input helps us level up Abun, so keep 'em coming!<br/>
<br/>
Cheers,<br/>
Team Abun<br/>
Abun.com"""


def article_generation_failed_email_body_for_admins(user: User, article: Article, reason: str):
    return f"""Hi Admin,<br/>
<br/>
{article.article_uid} article generation attempted by <a href="{ADMIN_DOMAIN}/all-users/{user.id}">{user.email}</a> user has failed. Below are the details of the failed article.<br/>
<br/>
User Name: {user.username}<br/>
Article Title: {article.title}<br/>
Keyword: {article.keyword.keyword}<br/>
Error Details: {reason}<br/>
<br/>
Best Regards,<br/> 
Team Abun<br/>
Abun.com"""


def no_plan_selection_email_body():
    return f"""Hi,<br/>
<br/>
Amin here, co-founder at Abun.com<br/>
<br/>
I see that you signed up but didn't proceed to choose any plan<br/>
Did you come across any errors? or were lost on how to proceed?<br/>
<br/>
Your help would be greatly appreciated.<br/>
It will help me improve the product :)<br/>
<br/>
<div>
<b>Amin Memon</b><br/>
Co-Founder at <a href="https://abun.com/" target="_blank">Abun</a> & <a href="https://draftss.com/" target="_blank">Draftss</a>
</div>
"""


def no_article_generated_email_body():
    return """Hi,<br/>
<br/>
Amin here, co-founder at Abun.com<br/>
<br/>
I see that you signed up but didn't create any article.<br/>
Did you came across any error? or were lost on how to proceed?<br/>
<br/>
Your help would be greatly appreciated.<br/>
It will help me improve the product.<br/>
<br/>
<div>
<b>Amin Memon</b><br/>
Co-Founder at <a href="https://abun.com/" target="_blank">Abun</a> & <a href="https://draftss.com/" target="_blank">Draftss</a>
</div>
"""


def first_article_generated_email_body():
    return """Hi,<br/>
<br/>
Amin here, co-founder at Abun.com<br/>
<br/>
I see that you tried generating an article using Abun.<br/>
What do you think about it? do you like it?<br/>
<br/>
<div>
<b>Amin Memon</b><br/>
Co-Founder at <a href="https://abun.com/" target="_blank">Abun</a> & <a href="https://draftss.com/" target="_blank">Draftss</a>
</div>
"""


def website_crawled_notification_email_body(username: str, website_domain: str):
    return f"""Hi {username},<br/>
<br/>
We are pleased to inform you that your website {website_domain} has been successfully crawled.<br/>
<br/>
Thank you for using Abun! If you have any questions or need further assistance, feel free to reach out to us via live chat.<br/>
<br/>
Best Regards,<br/>
Team Abun<br/>
Abun.com"""
