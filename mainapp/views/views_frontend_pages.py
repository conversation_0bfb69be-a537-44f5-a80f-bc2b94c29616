import datetime
import html
import secrets
import logging
import os
from typing import Dict
from zoneinfo import ZoneInfo
from urllib.request import urlopen
from urllib.error import HTTPError as UrllibHTTPError

import pytz
import stripe
import requests
import tldextract
from django.contrib.auth.hashers import check_password
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.db import IntegrityError, transaction
from django.db.models import Max, Value, BooleanField, Case, When, QuerySet, Prefetch
from django.http import JsonResponse
from django.contrib.auth.hashers import make_password
from django.core.files import File
from django.core.files.temp import NamedTemporaryFile
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.request import Request
from rest_framework_simplejwt.tokens import RefreshToken
from oauthlib.oauth2.rfc6749.errors import InvalidGrantError
from google_auth_oauthlib.flow import Flow
from oauth2client import client as google_oauth2client
from google.oauth2.credentials import Credentials

from mainapp import google_integration_utils
from AbunDRFBackend.settings import (STRIPE_CUSTOMER_PORTAL_LINK,  WP_RETURN_URL_DOMAIN, REDIS_TASK_DATA_DB,
                                     DEBUG)
from mainapp.decorators import subscription_required
from mainapp.json_responses import JsonResponseBadRequest, JsonResponseKeyError, JsonResponseRedirect, JsonResponseServerError
from mainapp.models import (AppSumoLicense, User, Website, Article, GoogleIntegration,
                            BlockDomain, KeywordProject, Keyword, KubernetesJob, ProgrammaticSeoTitle,
                            Competitor, Logo, WebsiteIndexation) 
from mainapp.serializers import (ArticleTitleTableDataSerializer, KeywordProjectKeywordsSerializer, AutomationProjectSerializer,
                                 WebsiteListSerializer)
from mainapp.stripe_utils import get_stripe_product_data, latest_invoice_is_open, setup_free_plan_for_ltd_user, get_stripe_product_data_by_id
from mainapp.utils import (send_verification_email, add_wordpress_integration, unescape_amp_char, fetch_appsumo_access_token,
                           fetch_appsumo_user_license_key, get_wordpress_routes, get_redis_connection, get_meta_description,
                           get_next_auto_scan_run_time)
from mainapp.tasks import celery_start_website_scanning, celery_fetch_gsc_position_for_wp
from mainapp.quickindex.indexing import index_url

logger = logging.getLogger('abun.frontend.pages')


@api_view(['POST'])
@permission_classes([AllowAny])
@transaction.atomic
def login_api(request: Request):
    """
    Handles user login.
    """
    try:
        email: str = request.data['email']
        password: str = request.data['password']

        # STEP 1: Get the appsumo code 
        appsumo_code: str = request.data.get('appsumo_code', None)

    except KeyError as k:
        logger.debug(f"key {k} was missing in frontend signup request")
        return JsonResponseKeyError()

    # clean & sanitize input
    email = email.strip().lower()

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return JsonResponse(status=401, data={'err_id': "INVALID_CREDENTIALS", 'message': "No Account Found. Try Signing Up."})

    if check_password(password, user.password):
        # Generate jwt tokens and send them back in response
        tokens = RefreshToken.for_user(user)
        user.last_login = datetime.datetime.now(tz=ZoneInfo('UTC'))
        user.last_login_using_google = False
        user.save()

        if appsumo_code:
            # STEP 2: Fetch a Temporary Access Token
            json_response = fetch_appsumo_access_token(appsumo_code)

            if not json_response:
                logger.error("Failed to fetch the access token from appsumo.")
                return JsonResponseBadRequest({'err_id': "FAILED_TO_FETCH_ACCESS_TOKEN"})

            else:
                # STEP 3: Use the Access Token to Fetch the User’s License
                access_token = json_response['access_token']
                refresh_token = json_response['refresh_token']
                license_key = fetch_appsumo_user_license_key(access_token, refresh_token)

                # STEP 4: Fetch the license data from db and add it to user account
                try:
                    appsumo_license = AppSumoLicense.objects.get(license_key=license_key)

                    if appsumo_license.user:
                        logger.error(f"{user.email} -> '{appsumo_license.license_key}' is already in use.")
                        return JsonResponseBadRequest({'err_id': "ALREADY_IN_USE"})

                    # Add license to user account
                    user.appsumo_licenses.add(appsumo_license)

                except AppSumoLicense.DoesNotExist:
                    logger.error(f"{user.email} -> No AppSumo license Found with '{license_key}'.")
                    return JsonResponseBadRequest({'err_id': "LICENSE_KEY_NOT_FOUND"})

                except Exception as err:
                    logger.critical(err)
                    return JsonResponseServerError()

        return JsonResponse(status=200, data={
            'refresh_token': str(tokens),
            'access_token': str(tokens.access_token),
        })

    else:
        # Wrong Password
        return JsonResponse(status=401, data={'err_id': "INVALID_CREDENTIALS", 'message': "Oops! That password doesn't match our records."})


@api_view(['POST'])
@permission_classes([AllowAny])
@transaction.atomic
def signup_api(request: Request):
    """
    Handles user signups.

    :param request: Django Rest Framework's Request object
    """
    if request.method == "POST":
        try:
            username: str = request.data['username']
            email: str = request.data['email']
            password: str = request.data['password']
            country: str = request.data['country']
            timezone: str = request.data['timezone']

            # STEP 1: Get the appsumo code 
            appsumo_code: str = request.data.get('appsumo_code', None)

        except KeyError as k:
            logger.debug(f"key {k} was missing in frontend signup request")
            return JsonResponseKeyError()

        # clean & sanitize inputs (except password)
        username = html.escape(username.strip())
        email = html.escape(email.strip().lower())
        country = html.escape(country.strip())

        if not any([username, email, password, country]):
            return JsonResponseBadRequest({'err_id': 'INVALID_INPUTS'})

        if len(username) > 150:
            return JsonResponseBadRequest({'err_id': 'USERNAME_TOO_LONG'})

        if len(email) > 254:
            return JsonResponseBadRequest({'err_id': 'EMAIL_TOO_LONG'})

        # check if domain is blocked
        domain_name = tldextract.extract(email).registered_domain
        if BlockDomain.objects.filter(domain=domain_name).exists():
            logger.debug(f"domain {domain_name} is blocked")
            return JsonResponseBadRequest({'err_id': 'DOMAIN_BLOCKED'})
            
        # do not proceed if email is bad
        try:
            validate_email(email)
        except ValidationError:
            logger.debug(f"email {email} validation failed during frontend signup")
            return JsonResponseBadRequest()

        # # create team
        # team: Team = Team(
        #     name=generate_default_team_name(username)
        # )
        # team.save()

        # ----------------- create user -----------------
        if not User.objects.filter(email=email).exists():
            account_exists = False
            user: User = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                country=country,
                user_tz=timezone
            )
            user.save()

            # ----------------- Send account email verification message -----------------
            send_verification_email(user)
        else:
            account_exists = True
            user = User.objects.get(email=email)

            if not check_password(password, user.password):
                return JsonResponseBadRequest({'err_id': 'ACCOUNT_ALREADY_EXISTS'})

        if appsumo_code:

            # STEP 2: Fetch a Temporary Access Token
            json_response = fetch_appsumo_access_token(appsumo_code)

            if not json_response:
                logger.error("Failed to fetch the access token from appsumo.")
                return JsonResponseBadRequest({'err_id': "FAILED_TO_FETCH_ACCESS_TOKEN"})

            else:
                # STEP 3: Use the Access Token to Fetch the User’s License
                access_token = json_response['access_token']
                refresh_token = json_response['refresh_token']
                license_key = fetch_appsumo_user_license_key(access_token, refresh_token)

                # STEP 4: Fetch the license data from db and add it to user account
                try:
                    appsumo_license = AppSumoLicense.objects.get(license_key=license_key)

                    if appsumo_license.user:
                        logger.error(f"{user.email} -> '{appsumo_license.license_key}' is already in use.")
                        return JsonResponseBadRequest({'err_id': "ALREADY_IN_USE"})

                    # Add license to user account
                    user.appsumo_licenses.add(appsumo_license)

                except AppSumoLicense.DoesNotExist:
                    logger.error(f"{user.email} -> No AppSumo license Found with '{license_key}'.")
                    return JsonResponseBadRequest({'err_id': "LICENSE_KEY_NOT_FOUND"})

                except Exception as err:
                    logger.critical(err)
                    return JsonResponseServerError()

            if not account_exists:
                # Setup the free plan
                setup_free_plan_for_ltd_user(user.email, country)

        # generate jwt tokens and send them back in response
        tokens = RefreshToken.for_user(user)

        return JsonResponse(status=200, data={
            'refresh_token': str(tokens),
            'access_token': str(tokens.access_token),
        })
    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def signup_plan_selection_page_api(request: Request):
    """
    Provides data for signup plan selection page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'GET':
        if user.stripe_pricing_id:
            return JsonResponse(
                status=302,
                data={'redirect_to': "dashboard"}
            )

        return JsonResponse(status=200, data={'message': "authenticated"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([AllowAny])
def logout_api(request: Request):
    """
    Blacklists provided refresh token.

    :param request: Django Rest Framework's Request object
    """
    refresh_token: str = request.data['refresh']
    token = RefreshToken(refresh_token)
    token.blacklist()
    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def logged_in_base_page_api(request: Request):
    """
    Provides data for signup plan selection page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website
    try:
        current_plan_data: Dict = get_stripe_product_data(user)
    except Exception as err:
        logger.critical(err)
        current_plan_data = {
                'name': "Trial",
                'display_name': "Free"
            }

    # print User's info if in dev mode (for debugging)
    if os.environ.get('CLOUDFLARE_R2_ENV') == 'dev':
        logger.info(f"User logged in: {user.username} - {user.email} - {user.country} - {datetime.datetime.now(tz=pytz.timezone('Asia/Kolkata')).strftime('%H:%M')}")

    # Get client's IP address
    client_ip = request.META.get("HTTP_X_FORWARDED_FOR")
    if client_ip:
        client_ip = client_ip.split(",")[0]  # Take the first IP if there are multiple IPs
    else:
        client_ip = request.META.get("REMOTE_ADDR")

    # Get location data based on client's IP address
    try:
        location_response = requests.get(
            f"http://ipinfo.io/{client_ip}/json", timeout=5
        )
        location_data = location_response.json()
    except Exception as err:
        logger.error(f"IP info API error: {err}")
        location_data = {
            "country": "US"
        }

    user.latest_ip_info = location_data
    user.save()
    competitors = Competitor.objects.filter(website=website).count()
    if request.method == 'GET':
        return JsonResponse(
            status=200,
            data={
                'user_id': user.id,
                'user_verified': user.verified,
                'username': user.username,
                'email': user.email,
                'survey_completed': True if DEBUG else user.survey_completed,
                'active_website_domain': website.domain if website else None,
                'active_website_logo': website.logo_url if website else None,
                'website_list': WebsiteListSerializer(user.website_set.all(), many=True).data,
                'currentPlanName': current_plan_data['name'],
                'competitor_length': competitors,
                'user_has_ltd_plans': user.has_ltd_plans,
                'display_name': current_plan_data['display_name'],
                'is_admin': user.admin,
                'current_active_website_pages_scanned': website.total_pages_scanned if website else 0,
            }
        )

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def contact_us_page_api(request: Request):
    """
    Authenticates & returns data related to dashboard page

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == "GET":
        return JsonResponse(status=200, data={
            'user_email': user.email
        })

    else:
        return JsonResponseBadRequest()


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# @subscription_required
# def content_plan_page_api(request: Request):
#     """
#     Authenticates & returns data for current website's content plan page (Only if content plan generation is done).

#     :param request: Django Rest Framework's Request object
#     """
#     user: User = request.user

#     if request.method == 'GET':
#         if not user.current_active_website:
#             return JsonResponse(status=200, data={
#                 'has_active_website': False,
#             })

#         if user.current_active_website.content_plan_generation_status == 'processing':
#             return JsonResponse(status=200, data={
#                 'has_active_website': True,
#                 'status': 'processing',
#                 'tips': ProTipSerializer(ProTip.objects.all(), many=True).data,
#             })
#         elif user.current_active_website.content_plan_generation_status == 'failed':
#             # reset the progress to 0
#             user.current_active_website.content_plan_task_progress = 0
#             user.current_active_website.save()
#             return JsonResponse(status=200, data={
#                 'has_active_website': True,
#                 'status': 'failed'
#             })
#         else:
#             return JsonResponse(status=200, data={
#                 'has_active_website': True,
#                 'status': 'done',
#             })

    # else:
    #     return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def articles_page_api(request: Request):
    """
    Authenticates & returns page data for articles page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'GET':
        response_data = {
            'current_page': "articles",
            'user_verified': user.verified,
            'has_active_website': bool(website),
            'all_integrations': user.all_integrations,
            'all_integrations_with_unique_id': user.all_integrations_with_unique_id,
            'google_search_console_integrated': website.google_search_console_integrated if website  else False,
            'articles_generated': user.articles_generated,
            'active_integration': user.active_integration,
        }
        # if website:
        #     response_data['content_plan_generation_status'] = website.content_plan_generation_status
        # else:
        #     response_data['content_plan_generation_status'] = ''

        return JsonResponse(status=200, data=response_data)

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def keywords_page_api(request: Request):
    """
    Authenticates & returns page data for keywords page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'GET':
        if website:
            return JsonResponse(status=200, data={
                'websiteDomain': user.current_active_website.domain,
                'google_search_console_integration': website.google_search_console_integrated if website else False,
                # 'content_plan_generation_status': website.content_plan_generation_status,
            })
        else:
            return JsonResponse(status=200, data={
                'websiteDomain': None,
                'content_plan_generation_status': '',
                'google_search_console_integration': False,
            })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def article_edit_page_api(request: Request):
    """
    Authenticates & returns page data for Article editor page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        try:
            article_uid: str = request.query_params['article_uid']
        except KeyError:
            return JsonResponseRedirect('dashboard')
        
        try:
            user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            return JsonResponseRedirect('dashboard')

        return JsonResponse(status=200, data={
            'page': "article_edit",
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def website_scanning_page_api(request: Request):
    """
    Authenticates & returns page data for Website scanning page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    # Auto schema stats
    total_pages_found = website.total_pages_scanned if website else 0
    total_pages_without_schema = website.total_pages_without_schema if website else 0
    pages_with_auto_schema_live = website.pages_with_auto_schema_live if website else 0

    # Calculate next auto scan run time
    next_auto_scan_run_time = None
    if website and website.auto_scan_website:
        next_auto_scan_run_time = get_next_auto_scan_run_time(user)

    if request.method == 'GET':
        return JsonResponse(status=200, data={
            'website_connected': bool(website),
            'domain': website and website.domain or "",
            'sitemap_urls': website and website.sitemap_urls or [],
            'is_crawling': website and website.is_crawling or False,
            'is_failed': website and website.is_failed or False,
            'crawling_task_queued': website and website.task_queued or False,
            'finding_sitemaps': website and website.finding_sitemaps or False,
            'has_more_pages': website and website.has_more_pages,
            'total_pages_found': total_pages_found,
            'total_pages_without_schema': total_pages_without_schema,
            'pages_with_auto_schema_live': pages_with_auto_schema_live,
            'auto_scraping_enabled': website and website.auto_scan_website or False,
            'auto_schema_enabled': website and website.auto_schema_enabled or False,
            'tools_loading_script_verified': website and website.tools_loading_script_verified,
            'auto_schema_used': website and website.auto_schema_used or False,
            'next_run_time': next_auto_scan_run_time,
            'encrypted_id': user.encrypted_id,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def keyword_project_titles_page_api(request: Request):
    """
    Authenticates & returns page data for keyword project titles page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'GET':
        try:
            keyword_hash: str = request.query_params['keyword_hash']
            keyword_project_id: str = request.query_params['keyword_project_id']
            page = int(request.GET.get('page', 1))
            per_page = int(request.GET.get('per_page', 10))
            search = request.GET.get('search', '')
            sort = request.GET.get('sort', '')
            
        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

        try:
            keyword: Keyword = user.keywords.get(keyword_md5_hash=keyword_hash)
            keyword_project: KeywordProject = user.keyword_projects.get(project_id=keyword_project_id)
            
            # Get pattern data for seo programmatic 
            programmatic_seo_titles = ProgrammaticSeoTitle.objects.filter(keyword_project=keyword_project)
            pattern = programmatic_seo_titles.first().pattern if programmatic_seo_titles.exists() else ""

        except Keyword.DoesNotExist:
            return JsonResponseBadRequest({'message': f"Keyword not found for: {keyword_hash}"})

        except KeywordProject.DoesNotExist:
            return JsonResponseBadRequest({'message': f"Keyword project not found for: {keyword_project_id}"})

        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

        articles: QuerySet[Article] = user.articles.filter(keyword=keyword).select_related(
            'keyword',
            'schedulearticleposting'
        ).only(
            'article_uid',
            'title',
            'internal_link_count',
            'external_link_count',
            'image_count',
            'word_count',
            'is_processing',
            'is_generated',
            'is_posted',
            'is_failed',
            'is_archived',
            'is_user_added',
            'article_link',
            'posted_to',
            'created_on',
            'generated_on',
            'posted_on',
            'feedback',
            'keyword__keyword',
            'keyword__keyword_md5_hash',
            'keyword__volume',
            'schedulearticleposting__schedule_on'
        ).prefetch_related(
            Prefetch('keyword__keywordproject_set', 
                    queryset=KeywordProject.objects.only('project_id'))
        )

        total = articles.count()
        # Paginate        
        start = (page - 1) * per_page
        end = start + per_page
        articles = articles[start:end]        
        titles_data = ArticleTitleTableDataSerializer(articles, many=True).data        
                
        return JsonResponse(status=200, data={
            'page': "keyword_project_titles",
            'titles_data': titles_data,
            'all_integrations': user.all_integrations,
            'all_integrations_with_unique_id': user.all_integrations_with_unique_id,
            'google_search_console_integrated': website.google_search_console_integrated if website else False,
            'user_verified': user.verified,
            'keyword': unescape_amp_char(keyword.keyword),
            'keywordHash': keyword.keyword_md5_hash,
            'keywordTraffic': keyword.volume,
            'difficultyScore': keyword.paid_difficulty,
            'keyword_project_id': keyword_project.project_id,
            'locationIsoCode': keyword_project.location_iso_code,
            'pattern': pattern,
            'total': total,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def settings_page_api(request: Request):
    """
    Authenticates & returns page data for Settings page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'GET':
        current_plan_data: Dict = get_stripe_product_data(user)
        plan_name: str = current_plan_data['name']
        display_name: str = current_plan_data['display_name']

        return JsonResponse(status=200, data={
            'page': "settings",
            'keyword_strategy': website.keyword_strategy,

            'all_integrations': user.all_integrations,
            'google_search_console_integration': website.google_search_console_integrated if website else False,

            'website_title': website.title,
            'website_description': website.description,
            'website_industry': website.industry,
            'website_icp': website.icp_text,

            'username': user.username,
            'email': user.email,
            'tz': user.user_tz,

            'website_domain': website.domain,
            'competitor_domains': list(website.competitor_set.all().values_list('domain', flat=True)) if user.current_active_website is not None else None,
            'competitor_edit_underway': user.kubernetesjob_set.filter(
                job_id__contains='editcompetitors', status='running'
            ).count() > 0,

            'feature_image_templates': [
                {
                    'template_id': "neon-style-with-text",
                    'template_name': "Neon Style with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794987/featured-image-template-sample/template-neon-text.png",
                    'tool_tip': "Glowing Horizon: Neon-infused design with bold text, perfect for modern, futuristic blogs.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 11
                },
                {
                    'template_id': "water-color-with-text",
                    'template_name': "Water Color with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-watercolor-text.png",
                    'tool_tip': "Artistic Brushstrokes: Soft watercolor textures with a vibrant touch, perfect for adding elegance with text.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 1
                },
                # {
                #     'template_id': "water-color-without-text",
                #     'template_name': "Water Color without Text",
                #     'sample_image_url': "https://cdn.abun.com/abun-media/dev/article-images/article-Yash-BIsht-4a11c486-4.png",
                #     'tool_tip': "Soft & Artistic: Pastel, hand-painted style that adds a gentle, creative touch to your blog."
                # },
                {
                    'template_id': "retro-with-text",
                    'template_name': "Retro with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796205/featured-image-template-sample/template-retro-text1.png",
                    'tool_tip': "Vintage Appeal: Classic, bold retro design perfect for nostalgic or themed content.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 13
                },
                {
                    'template_id': "comic-style-with-text",
                    'template_name': "Comic Style with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790298/featured-image-template-sample/template-comic-text.png",
                    'tool_tip': "Dynamic Comic Look: Bold, energetic layout that brings a comic-book feel to your post.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 5
                },
                {
                    'template_id': "doodle-sketch-with-text",
                    'template_name': "Doodle Sketch with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727789905/featured-image-template-sample/template-doodle-sketch-text.jpg",
                    'tool_tip': "Hand-drawn Charm: Casual, hand-drawn style with room for your title, adding a personal touch.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 2
                },
                # {
                #     'template_id': "doodle-sketch-without-text",
                #     'template_name': "Doodle Sketch without Text",
                #     'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790154/featured-image-template-sample/template-doodle-sketch.png",
                #     'tool_tip': "Creative Simplicity: Playful doodles without text, ideal for informal or creative posts."
                # },
                {
                    'template_id': "cyberpunk-with-text",
                    'template_name': "Cyberpunk with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk-text.png",
                    'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 9
                },
                {
                    'template_id': "grunge-with-text",
                    'template_name': "Grunge with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-grunge-text.png",
                    'tool_tip': "Urban Edge: A gritty, textured look with distressed elements, perfect for bold and rebellious themes.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 7
                },
                {
                    'template_id': "water-color-with-doodle-with-text",
                    'template_name': "Watercolor with Doodle with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-watercolor-doodle-text.png",
                    'tool_tip': "Artistic Whimsy: Soft watercolor splashes paired with playful doodles, ideal for creative and light-hearted topics.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 3
                },
                {
                    'template_id': "grunge-without-text",
                    'template_name': "Grunge without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-grunge.jpg",
                    'tool_tip': "Urban Edge: A raw, textured design with distressed elements, capturing a bold, rebellious vibe.",
                    'label': "Pro",
                    'order': 8
                },
                {
                    'template_id': "water-color-with-doodle-without-text",
                    'template_name': "Watercolor with Doodle without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-watercolor-doodle.png",
                    'tool_tip': "Artistic Whimsy: Gentle watercolor strokes combined with fun doodles, creating a creative and lively visual.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 9 or 4
                },
                {
                    'template_id': "cyberpunk-without-text",
                    'template_name': "Cyberpunk without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk.png",
                    'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 5 or 10
                },
                {
                    'template_id': "comic-style-without-text",
                    'template_name': "Comic Style without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790855/featured-image-template-sample/template-comic.png",
                    'tool_tip': "Graphic Impact: A striking, visual-only comic style that conveys energy and fun.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 4 or 6
                },
                {
                    'template_id': "retro-without-text",
                    'template_name': "Retro without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796614/featured-image-template-sample/template-retro1.png",
                    'tool_tip': "Vintage Vibes: Classic retro aesthetics with bold, nostalgic color schemes.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 7 or 14
                },
                {
                    'template_id': "neon-style-without-text",
                    'template_name': "Neon Style without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-neon.png",
                    'tool_tip': "Abstract Neon Dream: Bold and vibrant neon visuals without text, ideal for tech or creative posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 6 or 12
                },
                {
                    'template_id': "j14WwV5Vjj4R5a7XrB",
                    'template_name': "Compact Image Focus",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-two.png",
                    'tool_tip': "Balanced Layout: Small image next to bold text, perfect for articles with focused content.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 3 or 17
                },
                {
                    'template_id': "7wpnPQZzKKOm5dOgxo",
                    'template_name': "Bold Blue Contrast",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-three.png",
                    'tool_tip': "Eye-Catching Layout: Strong blue background to make the title pop, ideal for attention-grabbing posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 2 or 16
                },
                {
                    'template_id': "yKBqAzZ9xwB0bvMx36",
                    'template_name': "Image Dominance",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-four.png",
                    'tool_tip': "Visual First: Large image paired with minimal text, perfect for visually-driven blog posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 1 or 15
                },
                {
                    'template_id': "ok0l2K5mppOLZ3j1Yx",
                    'template_name': "Elegant Minimalism",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-one.png",
                    'tool_tip': "Simple Highlight: A sleek and simple design with a black background and yellow text, giving it a clean and timeless feel.",
                    'label': "Basic",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 10 or 18
                },
                {
                    'template_id': "kY4Qv7D8dAaLDB0qmP",
                    'template_name': "Bright Minimalism",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-five.png",
                    'tool_tip': "Vibrant Simplicity: A striking, high-contrast design with large black text on a green background, putting the focus entirely on the title.",
                    'label': "Basic",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 11 or 19
                },
            ],

            'selected_template': website.feature_image_template_id,
            'article_tone_of_voice': website.article_tone_of_voice,
            'external_backlinks_preference': website.external_backlinks_preference,
            'article_language_preference': website.article_language_preference,
            'max_internal_backlinks': website.max_internal_backlinks,
            'max_external_backlinks': website.max_external_backlinks,
            'internal_glossary_backlinks_preference': website.internal_glossary_backlinks_preference,
            'max_internal_glossary_backlinks': website.max_internal_glossary_backlinks,
            'current_plan_name': display_name,
            'images_file_format': website.images_file_format,
            'feature_image_required': website.feature_image_required,
            'image_source': website.image_source,
            'website_connection_limit': current_plan_data['metadata']['websites'],
            'article_context': website.article_context,
            'tone_of_article': website.tone_of_article,
            'scale_of_tone': website.scale_of_tone,
            'show_logo_on_featured_image': website.show_logo_on_featured_image,
            'toggle_toc': website.toggle_toc,
            'toggle_faq': website.toggle_faq, 
            'toggle_bullet_points':website.toggle_bullet_points,
            'toggle_meta_description':website.toggle_meta_description,
            'toggle_table':website.toggle_table,
            'toggle_tldr':website.toggle_tldr,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def plans_page_api(request: Request):
    """
    Authenticates & returns page data for Settings page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        if user.stripe_customer_id:
            user_has_pm = len(stripe.PaymentMethod.list(customer=user.stripe_customer_id, type="card")['data']) > 0
        else:
            user_has_pm = None

        # For storing active ltd plans name
        # active_ltd_plan_names = []

        # Get usage stats
        product_data: Dict = get_stripe_product_data(user)
        usage_stats = [
            {
                'title': "Websites Connected",
                'value': user.website_set.count(),
                'max': product_data['metadata']['websites'],
                'combined_string': ""
            },
            {
                'title': "All Time Articles Generated",
                'value': user.total_articles_generated,
                'max': "*",
                'combined_string': "",
                'unlimited': True
            },
            {
                'title': "Articles (Usage this Month)",
                'value': user.articles_generated,
                'max': product_data['metadata']['max_articles'],
                'combined_string': ""
            },
            {
                'title': "Keywords (Usage this Month)",
                'value': user.keywords_generated,
                'max': product_data['metadata']['max_keywords'],
                'combined_string': ""
            }
        ]

        # else:
        #     websites_limit = 0
        #     _website = ""

        #     articles_limit = 0
        #     _article = ""

        #     titles_limit = 0
        #     _title = ""

        #     keywords_limit = 0
        #     _keyword = ""

        #     for appsumo_license in user.active_ltd_plans:
        #         websites_limit += appsumo_license.website_limit
        #         articles_limit += appsumo_license.article_limit
        #         titles_limit += appsumo_license.title_limit
        #         keywords_limit += appsumo_license.keyword_limit

        #         _website += f"{appsumo_license.website_limit},"
        #         _article += f"{appsumo_license.article_limit},"
        #         _title += f"{appsumo_license.title_limit},"
        #         _keyword += f"{appsumo_license.keyword_limit},"

        #         active_ltd_plan_names.append(f"Tier {appsumo_license.tier}")

        #     # remove the last empty element
        #     combined_website_list = _website.split(",")
        #     combined_website_list.pop()

        #     usage_stats = [
        #         {
        #             'title': "Websites Connected",
        #             'value': user.website_set.count(),
        #             'max': websites_limit,
        #             'combined_string': " + ".join(_website[:-1].split(",")),
        #             'unlimited': True
        #         },
        #         {
        #             'title': "Article Titles",
        #             'value': user.titles_generated,
        #             'max': titles_limit,
        #             'combined_string': " + ".join(_title[:-1].split(",")),
        #             'unlimited': True
        #         },
        #         {
        #             'title': "Articles",
        #             'value': user.articles_generated,
        #             'max': articles_limit,
        #             'combined_string': " + ".join(_article[:-1].split(","))
        #         },
        #         {
        #             'title': "Keywords",
        #             'value': user.keywords_generated,
        #             'max': keywords_limit,
        #             'combined_string': " + ".join(_keyword[:-1].split(","))
        #         }
        #     ]

        # Get current stripe subscription details
        subscription = stripe.Subscription.retrieve(user.stripe_subscription_id)
        current_active_plan_name = subscription['plan']['nickname']

        return JsonResponse(status=200, data={
            'has_customer_id': bool(user.stripe_customer_id),
            'has_payment_method': bool(user_has_pm),
            'current_active_price_id_on_db': user.stripe_pricing_id,
            'current_active_price_id': subscription['items']['data'][0]['plan']['id'],
            'current_active_plan_id': subscription['id'],
            'current_active_plan_name': current_active_plan_name and current_active_plan_name.capitalize() or \
                                        current_active_plan_name,
            'current_active_period_end_date': datetime.datetime.fromtimestamp(subscription["current_period_end"],
                                                                              tz=datetime.UTC).strftime("%d/%m/%Y"),
            'stripe_customer_portal': STRIPE_CUSTOMER_PORTAL_LINK,
            'latest_invoice_is_open': latest_invoice_is_open(user.stripe_customer_id),
            'usage_stats': usage_stats,
            'ltd_enabled': user.has_ltd_plans,
            'ltd_plans': [f"Tier {appsumo_license.tier}" for appsumo_license in user.active_ltd_plans],
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def max_websites_page_api(request: Request):
    """
    Authenticates & returns page data for "max website limit reached" page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        current_plan_data: Dict = get_stripe_product_data(user)
        website_limit: int = current_plan_data['metadata']['websites']

        return JsonResponse(status=200, data={
            'limit': website_limit,
            'website_count': user.website_set.count(),
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def profile_page_api(request: Request):
    """
    Authenticates & returns page data for profile page.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if request.method == 'GET':
        domain = user.current_active_website.domain if user.current_active_website is not None else None

        try:
            if domain:
                if Logo.objects.filter(domain=domain).exists():
                    domain_logo_url = Logo.objects.get(domain=domain).image.url
                else:
                    img_temp = NamedTemporaryFile(delete=True)

                    img_temp.write(urlopen(f"https://logo.clearbit.com/{domain}").read())
                    img_temp.flush()

                    logo = Logo(domain=domain)
                    logo.image.save(domain, File(img_temp))

                    logo.save()

                    domain_logo_url = logo.image.url
            else:
                domain_logo_url = ""

        except UrllibHTTPError:
            domain_logo_url = ""

        return JsonResponse(status=200, data={
            'verified': user.verified,
            'username': user.username,
            'website': domain,
            'email': user.email,
            'tz': user.user_tz,
            'send_notif_email': user.send_notification_emails,
            'domain_logo_url': domain_logo_url,
            'current_plan_name': current_plan_name,
            'active_integration': user.active_integration,            
            # 'content_plan_generation_status': user.current_active_website.content_plan_generation_status if user.current_active_website is not None else None,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wordpress_integration_success(request: Request):
    """
    Authenticates and adds integration details to user website.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    wordpress_integrations = user.wordpress_integrations

    site_url: str = request.query_params['surl']
    user_login: str = request.query_params['ul']
    password: str = request.query_params['k']

    if wordpress_integrations.filter(site_url=site_url).exists():
        logger.info(f"'{site_url}' wordpress site is already integrated to user account.")
        return JsonResponse(status=200, data={'message': "OK"})

    # Fetch the wordpress routes
    routes = get_wordpress_routes(site_url)

    if not routes:
        return JsonResponseServerError()

    add_wordpress_integration(
        user=user,
        site_url=routes['site_url'],
        user_login=user_login,
        password=password,
    )

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def google_integrations_success(request: Request):
    """
    Adds google integration details to user website.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    integration_type: str = request.query_params['integration-type']
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponse(status=400, data={'message': "No website is connected", 'success': False})

    # Already integrated
    try:
        attribute_name = f"{integration_type.replace('-', '_')}_integrated"
        integrated = getattr(website, attribute_name)
    except AttributeError:
        return JsonResponse(status=400, data={'message': "Invalid Integration", 'success': False})

    if integrated:
        return JsonResponse(status=200, data={'message': "Already Integrated", 'success': True})

    try:

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            previous_state = redis_connection.get(f'oauth_state_{attribute_name}_{website.domain}')
            current_state = request.GET.get('state')

            if not previous_state or previous_state.decode() != current_state:
                return JsonResponse(status=400, data={'message': "Missing or Mismatch state", 'success': False})

            # Define the scope & clent secret file name 
            if integration_type == 'google-search-console':
                scopes = ['https://www.googleapis.com/auth/webmasters.readonly',                          
                          'https://www.googleapis.com/auth/indexing',
                        ]
                file_name = 'client_secret_gsc.json'
            # elif integration_type == 'google-analytics':
            #     scopes = ['https://www.googleapis.com/auth/analytics.readonly']
            #     file_name = 'client_secret_ga.json'
            # elif integration_type == 'google-drive':
            #     scopes = ['https://www.googleapis.com/auth/drive.readonly']
            #     file_name = 'client_secret_gd.json'
            else:
                return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION_TYPE"})

            # Define redirect URL
            redirect_url = f"{WP_RETURN_URL_DOMAIN}/integration/google/success"

            # Create authorization flow
            flow = Flow.from_client_secrets_file(
                os.path.join(os.path.dirname(os.path.abspath(__file__)), f'../google_client_secret/{file_name}'),
                scopes=scopes,
                redirect_uri=redirect_url
            )

            # Fetch the token
            flow.fetch_token(authorization_response=request.build_absolute_uri().replace('http:', 'https:'))

            # Delete stored oauth state from redis
            redis_connection.delete(f'oauth_state_{attribute_name}_{website.domain}')

    except InvalidGrantError:
        return JsonResponse(status=204, data={'message': "Integration Request Canceled", 'success': False})

    except Exception as e:
        logger.error(f"google_integrations_success() - {e}")
        return JsonResponseServerError()

    # Get the Credentials
    credentials = flow.credentials

    # Store the creds
    try:
        google_integration = GoogleIntegration(
            integration_type=integration_type,
            website=website,
            token=credentials.token,
            refresh_token=credentials.refresh_token,
            token_uri=credentials.token_uri,
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            scopes=credentials.scopes,
        )
        
        google_integration.save()
        
        celery_fetch_gsc_position_for_wp.delay(user.id)

    except IntegrityError:
        return JsonResponseBadRequest(additional_data={'message': "Some credential details are missing.", 'success': False})

    return JsonResponse(status=200, data={'message': "OK", 'success': True})


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# def webflow_integration_success(request: Request):
#     """
#     Adds google integration details to user website.

#     :param request: Django Rest Framework's Request object.
#     """
#     user: User = request.user
#     website: Website = user.current_active_website

#     # Already integrated
#     if website.webflow_integration:
#         return JsonResponse(status=200, data={'message': "Already Integrated", 'success': True})

#     with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
#         previous_state = redis_connection.get(f'webflow_oauth_state_{user.email}').decode()
#         current_state = request.GET.get('state')
#         auth_code = request.GET.get('code')
 
#         if previous_state != current_state:
#             return JsonResponse(status=400, data={'message': "Mismatch state", 'success': False})

#         # Fetch access token
#         access_token_endpoint = "https://api.webflow.com/oauth/access_token"
#         data = {
#             'client_id': WEBFLOW_CLIENT_ID,
#             'client_secret': WEBFLOW_CLIENT_SECRET,
#             'code': auth_code,
#             'grant_type': "authorization_code",
#             'redirect_uri': f"{WP_RETURN_URL_DOMAIN}/integration/webflow/success"
#         }
#         res = requests.post(access_token_endpoint, data=data)
#         response_data = res.json()

#         if not res.ok:
#             logger.error(f"Unable to fetch the access token. Response data -> '{response_data}' ")
#             return JsonResponse(status=500, data={'message': "Error while fetching the access token", 'success': False})

#         try:
#             access_token = response_data['access_token']
#             res = setup_webflow_account_for_autopublish(access_token)
#             logger.info(res)

#             if res['status'] == 200:
#                 data = res['data']
#                 site_id = data['site_id']
#                 collection_id = data['collection_id']
#                 site_url = data['site_url']

#                 add_webflow_integration(
#                     website,
#                     access_token,
#                     site_id,
#                     collection_id,
#                     site_url
#                 )

#             else:
#                 return JsonResponse(**res)

#         except Exception as err:
#             logger.critical(err)
#             return JsonResponseServerError()

#         # Delete stored oauth state from redis
#         redis_connection.delete(f'webflow_oauth_state_{user.email}')

#     return JsonResponse(status=200, data={'message': "OK", 'success': True})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def indexation_page_api(request: Request):
    """
    Authenticates & returns page data for indexation page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website
    url = f"https://{website.domain}/"
      
    gsc_integrated = website.google_search_console_integrated
    indexed_allowed = False
    
    
    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if gsc_integrated:
        credentials: Credentials = google_integration_utils.get_google_oauth2_credentials(user, "google-search-console") 
        indexed_allowed, error_reason = index_url(url, credentials)    
    
    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website).completed        
    except WebsiteIndexation.DoesNotExist:
        
        website_indexation = False

    if request.method == 'GET':
        response_data = {
            'current_page': "indexation",
            'user_verified': user.verified,
            'has_active_website': bool(website),
            'integration_done': gsc_integrated,
            'plan_name': current_plan_name,
            'indexation_done': website_indexation,
            "current_active_website": website.domain if website else "",
            "auto_indexing": website.auto_indexing if website else False,
            'indexed_allowed': indexed_allowed,
        }
        return JsonResponse(status=200, data=response_data)

    else:
        return JsonResponseBadRequest()


# New Keyword Research Page
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def keyword_projects_page_api(request: Request):
    """
    Authenticates & returns logged-in page data for keywords research page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    # Fetch current plan name
    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'
        
    latest_project = user.keyword_projects.order_by('-created_on').first()

    if latest_project:
        country_code = latest_project.location_iso_code
    else:
        country_code = "ZZ"

    return JsonResponse(
        status=200,
        data={
            "has_active_website": bool(website),
            "has_gsc_integration":website.google_search_console_integrated if website else False,
            "has_wp_integration":website.wp_integrated if website else False,
            "current_plan_name": current_plan_name,
            "articles_generated": user.articles_generated,
            "country_code": country_code,
            "selected_gsc_domain": website.selected_gsc_domain if website else "",
            "current_active_website": website.domain if website else "",
            "regenerate_competitor": website.regenerate_competitors if website else False,
        },
        safe=False,
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@subscription_required
def get_keyword_project_data(request: Request):
    """
    Authenticates & returns page data for a particular keyword project.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    project_id = request.query_params.get('project_id')

    if not project_id:
        return JsonResponse({'error': 'Project ID is required'}, status=400)

    try:
        # Optimize by prefetching keywords and using aggregation on article_set
        keyword_project = user.keyword_projects.prefetch_related(
            'keywords'
        ).get(project_id=project_id)
    except KeywordProject.DoesNotExist:
        return JsonResponse({'message': 'KeywordProject not found'}, status=404)

    # Use aggregation instead of a subquery to get the latest article timestamp per keyword.
    keywords_with_annotations = keyword_project.keywords.all().annotate(
        most_recent_art_title_timestamp=Max('article__created_on')
    ).annotate(
        titles_generated=Case(
            When(most_recent_art_title_timestamp__isnull=False, then=Value(True)),
            default=Value(False),
            output_field=BooleanField()
        )
    )
    
    kw_volume_false_count = keyword_project.keywords.filter(kw_volume=False).count()

    response_data = []
    keywords_data_with_no_volume = []

    for keyword in keywords_with_annotations:
        data = {
            'keyword': unescape_amp_char(keyword.keyword),
            'keywordHash': keyword.keyword_md5_hash,
            'keywordTraffic': keyword.volume,
            'difficultyScore': keyword.paid_difficulty,
            'titlesGenerated': keyword.titles_generated,
            'mostRecentArtTitleTimestamp': keyword.most_recent_art_title_timestamp.timestamp() if keyword.most_recent_art_title_timestamp else None,
            'kwVolume': getattr(keyword, 'kw_volume', False),            
        }

        if keyword.volume == 0:
            keywords_data_with_no_volume.append(data)
        else:
            response_data.append(data)

    # add keywords with 0 traffic volume at the end
    response_data.extend(keywords_data_with_no_volume)
    serializer = KeywordProjectKeywordsSerializer(response_data, many=True)

    return JsonResponse({"kwVolumeCount": kw_volume_false_count,"keywords": serializer.data}, status=200, safe=False)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@subscription_required
def automation_projects(request: Request):
    """
    Authenticates & returns page data for automation project page.
    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    # Check commented out to display the alert on the page
    # if 'Trial' in current_plan_name:
    #     return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    return JsonResponse(
        status=200,
        data={
            'integrations_with_unique_id': user.all_integrations_with_unique_id,
            'current_plan_name': current_plan_name
            
        },
        safe=False,
    )

@api_view(["GET"])
@permission_classes([IsAuthenticated])
@subscription_required
def get_automation_project_data(request: Request):
    """
    Authenticates & returns page data for a particular automation project.
    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    project_id = request.query_params["project_id"]

    if not project_id:
        return JsonResponse({"error": "Project ID is required"}, status=400)

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    try:
        automation_project = user.automation_projects.get(
            project_id=project_id
        )

        if not automation_project:
            logger.error(f"get_automation_project_data() - AutomationProject not found")
            return JsonResponse({"message": "AutomationProject not found"}, status=404)

        automation_projects_serializer = AutomationProjectSerializer(automation_project)

        return JsonResponse(
            status=200,
            data={
                "automation_project": automation_projects_serializer.data,
            },
            safe=False,
        )

    except Exception as e:
        logger.error(f"get_automation_project_data() - {e}")
        return JsonResponse({"error": "Internal Server Error"}, status=500)


@api_view(["POST"])
@permission_classes([AllowAny])
@transaction.atomic
def google_signup_login_success(request: Request):
    """
    Google signup login success
    :param request: Django Rest Framework's Request object.
    """
    try:
        state: str = request.data["state"]
        auth_code: str = request.data["code"]
        scope: str = request.data["scope"]
        country: str = request.data["country"]
        signup: str = request.data['signup']
    except KeyError:
        return JsonResponseKeyError()

    appsumo_code = None

    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        stored_state = redis_connection.get(state)
        appsumo_code_bytes = redis_connection.get(f"{state}__appsumo_code")
        appsumo_code = appsumo_code_bytes and appsumo_code_bytes.decode() or None

        if not stored_state or stored_state.decode() != "1":
            logger.error("'state' not found in redis!")
            return JsonResponse(status=400, data={'message': "State not found", 'success': False})

        # delete the store state
        redis_connection.delete(state)

    if signup:
        redirect_url = f"{WP_RETURN_URL_DOMAIN}/auth/accounts/google/signup"
    else:
        redirect_url = f"{WP_RETURN_URL_DOMAIN}/auth/accounts/google/login"

    try:
        # Fetch credentials
        credentials = google_oauth2client.credentials_from_clientsecrets_and_code(
            os.path.join(os.path.dirname(os.path.abspath(__file__)), f'../google_client_secret/client_secret_singup_login.json'),
            scope,
            auth_code,
            redirect_uri=redirect_url
        )

    except InvalidGrantError:
        return JsonResponse(status=400, data={'message': "Request Canceled!", 'success': False})

    except google_oauth2client.FlowExchangeError:
        return JsonResponse(status=400, data={'message': "Invalid request.", 'success': False})

    except Exception as e:
        logger.error(f"google_signup_login_success() - {e}")
        return JsonResponse(status=500, data={'message': "Internal Server Error", 'success': False})

    # Get profile info
    email = credentials.id_token['email']
    username = credentials.id_token['name']

    try:
        account_exists = True
        user = User.objects.get(email=email)
        user.verified = True
        user.last_login = datetime.datetime.now(tz=ZoneInfo('UTC'))
        user.last_login_using_google = True

    except User.DoesNotExist:
        account_exists = False
        user = User.objects.create_user(
            email=email,
            username=username,
            country=country,
            password=make_password(secrets.token_hex(12)),  # set a dummy password
            password_setup_required=True,
            verified=True,
            signup_using_google=True
        )

    user.save()

    website: Website = user.current_active_website

    if website and not website.is_crawled and not DEBUG:
        # Fetch the website sitemaps and Start crawling the pages
        celery_start_website_scanning.delay(website.domain, run='generate-summary')

    if appsumo_code:
        json_response = fetch_appsumo_access_token(appsumo_code)

        if not json_response:
            logger.error("Failed to fetch the access token from appsumo.")
            return JsonResponseBadRequest({'err_id': "FAILED_TO_FETCH_ACCESS_TOKEN"})

        else:
            access_token = json_response['access_token']
            refresh_token = json_response['refresh_token']
            license_key = fetch_appsumo_user_license_key(access_token, refresh_token)

            try:
                appsumo_license = AppSumoLicense.objects.get(license_key=license_key)

                if appsumo_license.user:
                    logger.error(f"{user.email} -> '{appsumo_license.license_key}' is already in use.")
                    return JsonResponseBadRequest({'err_id': "ALREADY_IN_USE"})

                # Add license to user account
                user.appsumo_licenses.add(appsumo_license)

            except AppSumoLicense.DoesNotExist:
                logger.error(f"{user.email} -> No AppSumo license Found with '{license_key}'.")
                return JsonResponseBadRequest({'err_id': "LICENSE_KEY_NOT_FOUND"})

            except Exception as err:
                logger.critical(err)
                return JsonResponseServerError()

        if not account_exists:
            # Setup the free plan
            setup_free_plan_for_ltd_user(user.email, country)

    # Create tokens
    tokens = RefreshToken.for_user(user)

    return JsonResponse(status=200, data={
        'success': True,
        'refresh_token': str(tokens),
        'access_token': str(tokens.access_token),
    })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@subscription_required
def create_article_page_api(request: Request):
    """
    Authenticates & returns page data for create article page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    latest_metadata = KubernetesJob.objects.filter(
        metadata__isnull=False
    ).exclude(metadata="").order_by('-created_on').values_list('metadata', flat=True).first()

    try:
        plan_name = get_stripe_product_data(user)['name']
    except Exception as err:
        logger.critical(f"get_stripe_product_data() err - {err} User-  {user.email} - {err}")
        plan_name = 'Trial'

    if website and not website.description:
        description = get_meta_description(website.domain)
        website.description = description or ""
        website.save()

    article = None
    if latest_metadata:
        article = user.articles.filter(article_uid=latest_metadata).exclude(context=None).last()
    
    if article:
        context = article.context
    else:
        context = ""

    return JsonResponse(status=200, data={
        "verified": user.verified,
        "article_context": context,
        "article_context_from_settings": user.article_context,
        'feature_image_templates': [
                {
                    'template_id': "neon-style-with-text",
                    'template_name': "Neon Style with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794987/featured-image-template-sample/template-neon-text.png",
                    'tool_tip': "Glowing Horizon: Neon-infused design with bold text, perfect for modern, futuristic blogs.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 11
                },
                {
                    'template_id': "water-color-with-text",
                    'template_name': "Water Color with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-watercolor-text.png",
                    'tool_tip': "Artistic Brushstrokes: Soft watercolor textures with a vibrant touch, perfect for adding elegance with text.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 1
                },
                {
                    'template_id': "retro-with-text",
                    'template_name': "Retro with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796205/featured-image-template-sample/template-retro-text1.png",
                    'tool_tip': "Vintage Appeal: Classic, bold retro design perfect for nostalgic or themed content.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 13
                },
                {
                    'template_id': "comic-style-with-text",
                    'template_name': "Comic Style with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790298/featured-image-template-sample/template-comic-text.png",
                    'tool_tip': "Dynamic Comic Look: Bold, energetic layout that brings a comic-book feel to your post.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 5
                },
                {
                    'template_id': "doodle-sketch-with-text",
                    'template_name': "Doodle Sketch with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727789905/featured-image-template-sample/template-doodle-sketch-text.jpg",
                    'tool_tip': "Hand-drawn Charm: Casual, hand-drawn style with room for your title, adding a personal touch.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 2
                },
                {
                    'template_id': "cyberpunk-with-text",
                    'template_name': "Cyberpunk with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk-text.png",
                    'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 9
                },
                {
                    'template_id': "grunge-with-text",
                    'template_name': "Grunge with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-grunge-text.png",
                    'tool_tip': "Urban Edge: A gritty, textured look with distressed elements, perfect for bold and rebellious themes.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 7
                },
                {
                    'template_id': "water-color-with-doodle-with-text",
                    'template_name': "Watercolor with Doodle with Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-watercolor-doodle-text.png",
                    'tool_tip': "Artistic Whimsy: Soft watercolor splashes paired with playful doodles, ideal for creative and light-hearted topics.",
                    'label': "Premium",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 99 or 3
                },
                {
                    'template_id': "grunge-without-text",
                    'template_name': "Grunge without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-grunge.jpg",
                    'tool_tip': "Urban Edge: A raw, textured design with distressed elements, capturing a bold, rebellious vibe.",
                    'label': "Pro",
                    'order': 8
                },
                {
                    'template_id': "water-color-with-doodle-without-text",
                    'template_name': "Watercolor with Doodle without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-watercolor-doodle.png",
                    'tool_tip': "Artistic Whimsy: Gentle watercolor strokes combined with fun doodles, creating a creative and lively visual.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 9 or 4
                },
                {
                    'template_id': "cyberpunk-without-text",
                    'template_name': "Cyberpunk without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk.png",
                    'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 5 or 10
                },
                {
                    'template_id': "comic-style-without-text",
                    'template_name': "Comic Style without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790855/featured-image-template-sample/template-comic.png",
                    'tool_tip': "Graphic Impact: A striking, visual-only comic style that conveys energy and fun.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 4 or 6
                },
                {
                    'template_id': "retro-without-text",
                    'template_name': "Retro without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796614/featured-image-template-sample/template-retro1.png",
                    'tool_tip': "Vintage Vibes: Classic retro aesthetics with bold, nostalgic color schemes.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 7 or 14
                },
                {
                    'template_id': "neon-style-without-text",
                    'template_name': "Neon Style without Text",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-neon.png",
                    'tool_tip': "Abstract Neon Dream: Bold and vibrant neon visuals without text, ideal for tech or creative posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 6 or 12
                },
                {
                    'template_id': "j14WwV5Vjj4R5a7XrB",
                    'template_name': "Compact Image Focus",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-two.png",
                    'tool_tip': "Balanced Layout: Small image next to bold text, perfect for articles with focused content.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 3 or 17
                },
                {
                    'template_id': "7wpnPQZzKKOm5dOgxo",
                    'template_name': "Bold Blue Contrast",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-three.png",
                    'tool_tip': "Eye-Catching Layout: Strong blue background to make the title pop, ideal for attention-grabbing posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 2 or 16
                },
                {
                    'template_id': "yKBqAzZ9xwB0bvMx36",
                    'template_name': "Image Dominance",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-four.png",
                    'tool_tip': "Visual First: Large image paired with minimal text, perfect for visually-driven blog posts.",
                    'label': "Pro",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 1 or 15
                },
                {
                    'template_id': "ok0l2K5mppOLZ3j1Yx",
                    'template_name': "Elegant Minimalism",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-one.png",
                    'tool_tip': "Simple Highlight: A sleek and simple design with a black background and yellow text, giving it a clean and timeless feel.",
                    'label': "Basic",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 10 or 18
                },
                {
                    'template_id': "kY4Qv7D8dAaLDB0qmP",
                    'template_name': "Bright Minimalism",
                    'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-five.png",
                    'tool_tip': "Vibrant Simplicity: A striking, high-contrast design with large black text on a green background, putting the focus entirely on the title.",
                    'label': "Basic",
                    'order': plan_name in ["Basic", "LTD", "Trial"] and 11 or 19
                },
            ],

            'current_plan_name': plan_name,
            'selected_template': website and website.feature_image_template_id or "water-color-with-text",
            'article_tone_of_voice': website and website.article_tone_of_voice or "exciting",
            'external_backlinks_preference': website and website.external_backlinks_preference or "no-follow",
            'max_internal_backlinks': website and website.max_internal_backlinks or 5,
            'max_external_backlinks': website and website.max_external_backlinks or 2,
            'images_file_format': website and website.images_file_format or "png",
            'feature_image_required': website and website.feature_image_required or True,
            'image_source': website and website.image_source or "no_image",
            'tone_of_article': website and website.tone_of_article or "off",
            'scale_of_tone': website and website.scale_of_tone or 3,
            'show_logo_on_featured_image': website and website.show_logo_on_featured_image or False,
            'description': website and website.description or "",
            'domain': website and website.domain or "",
            'toggle_toc': website and website.toggle_toc or True,
            'toggle_faq': website and website.toggle_faq or True, 
            'toggle_bullet_points': website and website.toggle_bullet_points or True,
            'toggle_meta_description': website and website.toggle_meta_description or True,
            'toggle_table': website and website.toggle_table or True,
            'toggle_tldr': website and website.toggle_tldr or True,
    })
