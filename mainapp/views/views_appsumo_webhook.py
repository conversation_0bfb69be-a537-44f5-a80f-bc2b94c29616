import json
import datetime
import logging
from typing import Dict, List

from django.http import HttpResponse, JsonResponse
from django.db.utils import IntegrityError
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from rest_framework.decorators import api_view
from rest_framework.request import Request

import pytz

from mainapp.decorators import cors_allow_all
from mainapp.models import AppSumoLicense
from mainapp.utils import get_next_renewal_date


logger = logging.getLogger('abun.appsumo_webhook')


@cors_allow_all
@api_view(['POST'])
def appsumo_webhook(request: Request) -> HttpResponse:
    """
    AppSumo events webhook.

    :param request: Django Rest Framework's Request object
    """
    payload = request.body
    data: Dict = json.loads(payload)

    # --------------------- Handle the events ---------------------
    if data.get('test', False):
        logger.debug("------------- APPSUMO: TESTING EVENT -------------")
        handle_testing_webhook_signal(data['license_key'], plan_id=data['plan_id'], status=data['license_status'])

    elif data['event'] == 'purchase':
        logger.debug("------------- APPSUMO: PURCHASE -------------")
        created = store_appsumo_license(data)

        if not created:
            return JsonResponse(status=400, data={"message": 'license key already exists', "success": False})

    elif data['event'] == 'activate':
        logger.debug("------------- APPSUMO: ACTIVATE -------------")
        updated = update_appsumo_license_data(data['license_key'],
                                              ['license_status', 'activated_on', 'reason'],
                                              ['active', datetime.datetime.now(tz=pytz.timezone('UTC')),
                                               get_event_reason(data)])

        if not updated:
            return JsonResponse(status=400, data={"message": 'license key not found,', "success": False})

    elif data['event'] in ['upgrade', 'downgrade']:
        logger.debug(f"------------- APPSUMO: TIER UPDATE -------------")
        updated = upgrade_or_downgrade_appsumo_license(data)

        if not updated:
            return JsonResponse(status=400, data={"message": 'license key not found,', "success": False})

    elif data['event'] == 'deactivate':
        logger.debug("------------- APPSUMO: DEACTIVATE -------------")
        updated = update_appsumo_license_data(data['license_key'],
                                              ['license_status', 'deactivated_on', 'reason'],
                                              ['deactivated', datetime.datetime.now(tz=pytz.timezone('UTC')),
                                               get_event_reason(data)])

        if not updated:
            return JsonResponse(status=400, data={"message": 'license key not found,', "success": False})

    else:
        logger.error(f"Unhandled event type {data['event']}")
    
    return JsonResponse(status=200, data={"success": True})


def store_appsumo_license(webhook_data: Dict) -> bool:
    """
    Used to store the appsumo license key
    :param webhook_data: AppSumo webhook data
    """
    try:
        appsumo = AppSumoLicense(
            license_key=webhook_data['license_key'],
            plan_id=webhook_data['plan_id'],
            license_status="inactive",
            test_mode=False,
            tier=webhook_data['tier'],
            reason=get_event_reason(webhook_data),
            next_renewal_date=get_next_renewal_date()
        )
        appsumo.save()

        return True

    except IntegrityError:
        logger.error(f"'{webhook_data['license_key']}' license key is already exists")

    except KeyError as key:
        logger.error(f"Missing required key {key}")

    return False


def update_appsumo_license_data(license_key: str,
                                attribute_name: str | List[str],
                                new_value: any) -> bool:
    """
    Updates the AppSumo license data
    :param license_key: AppSumo license key
    :param attribute_name: Attribute name to update
    :param new_value: New value
    """
    try:
        appsumo = AppSumoLicense.objects.get(license_key=license_key)

        if isinstance(attribute_name, str):
            setattr(appsumo, attribute_name, new_value)
        else:
            for attribute, value in zip(attribute_name, new_value):
                setattr(appsumo, attribute, value)

        appsumo.save()
        return True

    except AppSumoLicense.DoesNotExist:
        logger.error(f"'{license_key}' license key is not found in our database")
        return False


def upgrade_or_downgrade_appsumo_license(webhook_data: Dict) -> bool:
    """
    Used to upgrade or downgrade the AppSumo license
    :param webhook_data: AppSumo webhook data
    """
    try:
        prev_license_key = webhook_data['prev_license_key']
        new_license_key = webhook_data['license_key']
        tier = webhook_data['tier']
        plan_id = webhook_data['plan_id']

    except KeyError as key:
        logger.error(f"Missing required key {key}")
        return False

    try:
        # Check if previous licnese exists
        # NOTE: AppSumo will send a separate webhook event to deactivate the license key
        prev_appsumo_license = AppSumoLicense.objects.get(license_key=prev_license_key)

    except AppSumoLicense.DoesNotExist:
        logger.error(f"'{prev_license_key}' license key is not found in our database")
        return False

    try:
        new_appsumo_license = AppSumoLicense(
            license_key=new_license_key,
            plan_id=plan_id,
            license_status="active",  # This is an upgrade/downgrade so keep the license active
            test_mode=False,
            tier=tier,
            reason=get_event_reason(webhook_data),
            next_renewal_date=get_next_renewal_date()
        )
        new_appsumo_license.save()

        # Add user if exists
        if prev_appsumo_license.user:
            user = prev_appsumo_license.user
            user.appsumo_licenses.add(new_appsumo_license)

        return True

    except IntegrityError:
        logger.error(f"'{new_license_key}' already exists.")

    return False


def handle_testing_webhook_signal(license_key: str, **kwargs):
    """
    Handles the testing webhook signal
    :param license_key: AppSumo license key
    """
    appsumo, created = AppSumoLicense.objects.get_or_create(license_key=license_key)

    plan_id = kwargs.get("plan_id")
    updated_status = kwargs.get("status")

    if created:
        appsumo.license_status = "inactive"

    if plan_id:
        appsumo.plan_id = plan_id

    if updated_status:
        appsumo.license_status = updated_status

    appsumo.test_mode = True
    appsumo.save()


def get_event_reason(webhook_data: Dict) -> str | None:
    """
    Returns the Event reason from the webhook data.
    :param webhook_data: Webhook Data
    """
    extra_info: Dict | None = webhook_data.get('extra')

    if extra_info:
        return extra_info.get('reason')

    return None
