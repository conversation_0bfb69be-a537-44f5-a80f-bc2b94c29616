import logging
import base64
import re
import json
from typing import List, Dict

import uj<PERSON>
import redis
from django.http import JsonResponse
from rest_framework.decorators import api_view
from rest_framework.request import Request

from mainapp.decorators import cors_allow_all
from mainapp.json_responses import JsonResponseBadRequest
from mainapp.models import Keyword, KubernetesJob, KubernetesJobLogs, Website, Article
from mainapp.utils import download_and_save_image, download_and_save_image_bytes, segmind_ai_img_gen_api_call, get_redis_connection
from mainapp.chroma_db_manager import ChromaDBManager
from AbunDRFBackend.settings import REDIS_TASK_DATA_DB

logger = logging.getLogger('abun.k8_apis')


@cors_allow_all
@api_view(['POST'])
def filter_existing_keywords(request: Request) -> JsonResponse:
    """
    This endpoint filters out keywords existing in our database and returns a dictionary containing keywords
    separated into 2 lists - existing and non-existing.

    :param request: Django Rest Framework's Request object
    """
    data_string: str = request.data['list_of_keywords']
    list_of_keywords: List[str] = ujson.loads(data_string)

    existing_keywords: List[str] = list(
        Keyword.objects.filter(keyword__in=list_of_keywords).values_list('keyword', flat=True)
    )
    non_existing_keywords: List[str] = list(set(list_of_keywords) - set(existing_keywords))

    return JsonResponse(status=200, data={
        'existing_keywords': existing_keywords,
        'non_existing_keywords': non_existing_keywords
    })


@cors_allow_all
@api_view(['POST'])
def log_k8_job_messages(request: Request) -> JsonResponse:
    """
    Saves log message from Kubernetes Job to database.
    """
    job_id: str = request.data['job_id']
    log_type: str = request.data['log_type']
    log_message: str = request.data['log_message']

    try:
        job = KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"KubernetesJob with job_id {job_id} not found.")
        return JsonResponse(status=404, data={'message': f"KubernetesJob with job_id {job_id} not found."})

    # Create the log
    k8_job_log = KubernetesJobLogs(
        job=job,
        type=log_type,
        message=log_message
    )
    k8_job_log.save()

    return JsonResponse(status=200, data={'message': "OK"})


@cors_allow_all
@api_view(['POST'])
def update_content_plan_progress(request: Request) -> JsonResponse:
    """
    Updates the content plan progress of a website in DRF.
    """
    job_id: str = request.data['job_id']
    progress: int = request.data['progress']

    try:
        website: Website = KubernetesJob.objects.get(job_id=job_id).website
        website.content_plan_task_progress = progress
        website.save()
    except KubernetesJob.DoesNotExist:
        logger.error(f"KubernetesJob with job_id {job_id} not found.")
        return JsonResponse(status=404, data={'message': f"KubernetesJob with job_id {job_id} not found."})

    return JsonResponse(status=200, data={'message': "OK"})


@cors_allow_all
@api_view(['POST'])
def save_article_image_k8(request: Request) -> JsonResponse:
    """
    Saves the AI generated article image
    :param request: Django Rest Framework's Request object
    """
    image_url_or_content: str = request.data['image']
    article_uid: str = request.data['article_uid']

    try:
        _ = Article.objects.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponse(status=400, data={'err_id': "NO_SUCH_ARTICLE_FOUND", 'message': f"No article found with '{article_uid}'"})

    if not image_url_or_content.startswith('http'):
        base64_data: str = image_url_or_content.split(',')[1]
        image_data: bytes = base64.b64decode(base64_data)
        image_url = download_and_save_image_bytes(image_data, article_uid)

    else:
        image_url = download_and_save_image(image_url_or_content, article_uid)

    if image_url:
        return JsonResponse(status=200, data={'image_url': image_url})

    return JsonResponse(status=400, data={'err_id': "IMAGE_NOT_SAVED", 'message': "Image is not saved"})


@cors_allow_all
@api_view(['POST'])
def save_segment_article_image_k8(request: Request) -> JsonResponse:
    """
    Saves the segment AI generated article image
    :param request: Django Rest Framework's Request object
    """
    try:
        flux_dev_prompt: str = request.data['flux_dev_prompt']
        article_uid: str = request.data['article_uid']
    except Exception as e:
        logger.error(f"Error in request data: {e}")
        return JsonResponse(status=400, data={'err_id': "INVALID_REQUEST_DATA", 'message': "Invalid request data"})

    try:
        image_url = segmind_ai_img_gen_api_call(flux_dev_prompt, article_uid, type='in_line_image').get('image_url')
        if image_url:
            return JsonResponse(status=200, data={'image_url': image_url})
    except Exception as e:
        logger.error(f"Error in generating AI image: {e}")
        return JsonResponse(status=500, data={'err_id': "AI_IMAGE_GENERATION_ERROR", 'message': "Error in generating AI image"})

    return JsonResponse(status=400, data={'err_id': "IMAGE_NOT_SAVED", 'message': "Image is not saved"})


@cors_allow_all
@api_view(['POST'])
def fetch_article_internal_links(request: Request) -> JsonResponse:
    """
    Returns the intenal links
    :param request: Django Rest Framework's Request object
    """
    try:
        job_id: str = request.data['job_id']
        phrase: str = request.data['phrase']
        body_content: str = request.data['body_content']
    except KeyError as key:
        logger.error(f"Missing required Key: {key}")
        return JsonResponse(status=400, data={'err_id': "INVALID_REQUEST_DATA", 'message': "Invalid request data"})

    try:
        k8_job = KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"KubernetesJob with job_id {job_id} not found.")
        return JsonResponse(status=404, data={'message': f"KubernetesJob with job_id {job_id} not found."})

    # Get the article UID from metadata
    article_uid = k8_job.metadata

    try:
        article = Article.objects.get(article_uid=article_uid)
    except Article.DoesNotExist:
        logger.error(f"Article with article_uid {article_uid} not found.")
        return JsonResponse(status=404, data={'message': f"Article with article_uid {article_uid} not found."})

    # Get the website
    website = article.website

    # Extract sentence containing the phrase. Default to the phrase itself
    search_text = phrase

    # Escape special regex characters in phrase
    escaped_phrase = re.escape(phrase)

    # Pattern to match a sentence containing the phrase
    pattern = f'[^.!?]*{escaped_phrase}[^.!?]*[.!?]'
    match = re.search(pattern, body_content, re.IGNORECASE)

    # If a sentence is found, use it as the search text
    if match:
        search_text = match.group(0).strip()
        logger.info(f"Found sentence: '{search_text}'")
    else:
        logger.info(f"No matching sentence found for '{phrase}' phrase.")

    # Create Chroma DB manager
    chromaDBManager = ChromaDBManager()

    # First try to find similar pages with the sentence
    similar_pages = chromaDBManager.find_similar_pages(search_text, website.id)
    logger.info(f"Found {len(similar_pages)} similar pages using sentence")

    # If no similar pages found with the sentence, try with the original phrase
    if not similar_pages and search_text != phrase:
        logger.info(f"No similar pages found with sentence. Trying with original phrase: '{phrase}'")
        similar_pages = chromaDBManager.find_similar_pages(phrase, website.id)

        if similar_pages:
            logger.info(f"Found {len(similar_pages)} similar pages using original phrase")

    # If still no similar pages found, return an empty list
    if not similar_pages:
        return JsonResponse(status=200, data={'success': True, 'internal_links': []})

    # Sort the similar pages by similarity score
    similar_pages.sort(key=lambda page: page["similarity"], reverse=True)

    return JsonResponse(status=200, data={'success': True, 'internal_links': similar_pages})


@cors_allow_all
@api_view(['GET'])
def fetch_task_data(request: Request) -> JsonResponse:
    """
    Returns the task data
    :param request: Django Rest Framework's Request object
    """
    try:
        job_id: str = request.query_params['job_id']
    except KeyError as key:
        logger.error(f"Missing required Key: {key}")
        return JsonResponse(status=400, data={'err_id': "INVALID_REQUEST_DATA", 'message': "Invalid request data"})

    try:
        KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"KubernetesJob with job_id {job_id} not found.")
        return JsonResponse(status=404, data={'message': f"KubernetesJob with job_id {job_id} not found."})

    task_data: Dict | None = None

    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        task_data = redis_connection.get(job_id)

    if not task_data:
        return JsonResponseBadRequest()

    json_data = json.loads(task_data)
    return JsonResponse(status=200, data=json_data)


@cors_allow_all
@api_view(['POST'])
def save_task_data(request: Request) -> JsonResponse:
    """
    Saves the task data
    :param request: Django Rest Framework's Request object
    """
    try:
        # job_id: str = request.data['job_id']
        key_name: str = request.data['key_name']
        key_value: str = request.data['key_value']
        ttl: int = request.data.get('tll', None)
    except KeyError as key:
        logger.error(f"Missing required Key: {key}")
        return JsonResponse(status=400, data={'err_id': "INVALID_REQUEST_DATA", 'message': "Invalid request data"})

    # try:
    #     KubernetesJob.objects.get(job_id=job_id)
    # except KubernetesJob.DoesNotExist:
    #     logger.error(f"KubernetesJob with job_id {job_id} not found.")
    #     return JsonResponse(status=404, data={'message': f"KubernetesJob with job_id {job_id} not found."})

    # Add the state to redis task data db
    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        redis_connection.set(key_name, key_value)
        if ttl:
            redis_connection.expire(key_name, ttl)

    return JsonResponse(status=200, data={'success': True})
