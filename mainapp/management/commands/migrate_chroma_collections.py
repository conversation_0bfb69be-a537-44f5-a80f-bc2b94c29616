"""
======================================================
Script to store all scraped website pages to chromaDB.
======================================================
"""

from django.db import transaction
from django.db.models import QuerySet, When, Case, Value
from django.core.management import BaseCommand

from mainapp.models import Website, WebPage
from mainapp.chroma_db_manager import ChromaDBManager


class Command(BaseCommand):
    def handle(self, *args, **options):
        with transaction.atomic():
            websites: QuerySet[Website] = Website.objects.filter(is_crawled=True)
            chromaDBManager = ChromaDBManager()

            for website in websites.iterator():
                self.stdout.write(f"[*] Running for {website.domain}.")

                webpages: QuerySet[WebPage] = website.webpage_set.all()

                if webpages.exists():
                    webpage_map = chromaDBManager.bulk_add_pages(webpages)
                    self.stdout.write(f"[*] Pages added to chromadb. Updating webpages embedding ids...")
                else:
                    self.stdout.write(f"[*] No WebPage found for {website.domain}.")
                    continue

                # Update embedding IDs
                case_statement = Case(
                    *[When(id=webpage_id, then=Value(embedding_id)) for webpage_id, embedding_id in webpage_map.items()]
                )
                updated_count = WebPage.objects.filter(id__in=webpage_map.keys()).update(embedding_id=case_statement)

                # Verify all updates were successful
                if updated_count != len(webpage_map):
                    self.stdout.write(f"[x] Not all pages were updated. Expected {len(webpage_map)}, updated {updated_count}")
                else:
                    self.stdout.write(f"[*] Embedding ids updated.")

            self.stdout.write(f"[*] Done.")
