"""
====================================================================================================================
This script will be run periodically using cronjob to send email notification if user hasn't generated any article or generated first article
within 2 hours after signup
====================================================================================================================
"""
from typing import Any, List
from concurrent.futures import ThreadPoolExecutor

from django.utils import timezone
from django.db.models import QuerySet
from django.core.management import BaseCommand

from mainapp.models import User
from mainapp.utils import send_email
from AbunDRFBackend.settings import AMIN_EMAIL
from mainapp.email_messages import no_article_generated_email_body, first_article_generated_email_body


class Command(BaseCommand):
    email_body_for_no_article_generated = no_article_generated_email_body()
    email_body_for_first_article_generated = first_article_generated_email_body()

    def handle(self, *args: Any, **options: Any) -> None:
        today_datetime = timezone.now()
        users_email_notifier: List[User] = []

        users: QuerySet[User] = User.objects.exclude(
            stripe_subscription_id=None,
        ).filter(
            total_articles_generated__gte=0,
            article_email_sent=False
        )

        for user in users:
            difference_in_hours = (today_datetime - user.date_joined).total_seconds() // 3600

            if difference_in_hours > 24:
                user.article_email_sent = True
                user.save()
                continue

            if difference_in_hours >= 2:
                self.stdout.write(f"[*] {user.email} is eligible for sending article email, adding user to 'users_email_notifier'")
                users_email_notifier.append(user)

        if users_email_notifier:
            executor = ThreadPoolExecutor(max_workers=min(len(users_email_notifier), 30))
            for message in executor.map(self.send_aricle_email, users_email_notifier):
                self.stdout.write(message)

    def send_aricle_email(self, user: User) -> str:
        if user.total_articles_generated == 0:
            send_email(
                user.email,
                AMIN_EMAIL,
                "Amin Memon",
                "abun/help",
                self.email_body_for_no_article_generated,
                reply_to=AMIN_EMAIL
            )

        else:
            send_email(
                user.email,
                AMIN_EMAIL,
                "Amin Memon",
                "abun/help",
                self.email_body_for_first_article_generated,
                reply_to=AMIN_EMAIL
            )

        # update user article email field
        user.article_email_sent = True
        user.save()

        return f"[*] Article sent to {user.email} user."
