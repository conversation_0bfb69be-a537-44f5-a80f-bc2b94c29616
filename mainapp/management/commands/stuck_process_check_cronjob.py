"""
====================================================================================================================
This script will be run periodcally using cronjob to mark the stuck k8 jobs as failed & send an email to admins.
====================================================================================================================
"""

import os
import sys
import datetime
from typing import List

from django.utils import timezone
from django.db import transaction
from django.db.models import QuerySet
from django.core.management import BaseCommand

from mainapp.utils import send_email
from AbunDRFBackend.settings import ABUN_NOTIFICATION_EMAIL, AMIN_EMAIL, ADIL_EMAIL
from mainapp.models import (KubernetesJob, Article, Website, WebPage, WebsiteScanQueue,
                            User, ArticleInternalLinkQueue)
from mainapp.email_messages import stuck_process_body_for_article_generation, stuck_process_body_for_website_scanning


class Command(BaseCommand):
    def __init__(self) -> None:
        super().__init__()
        self.job_id: str | None = None
        self.job: KubernetesJob | None = None
        self.article: Article | None = None
        self.website: Website | None = None
        self.k8_jobs_failed: int = 0  # Stores the no. of k8 jobs failed
        self.article_generation_task_failed: int = 0  # Stores the no. of article generation tasks failed
        self.website_scanning_task_failed: int = 0  # Stores the no. of website scanning tasks failed
        self.competitor_finder_task_failed: int = 0  # Stores the no. of competitor finder tasks failed
        self.article_internal_link_queue_failed: int = 0  # Stores the no. of article internal link queue tasks failed
        self.email_messages: List = []

    def mark_article_generation_as_failed_or_completed(self):
        """
        Used to mark the Article generation task as failed or completed
        """
        if self.article.is_generated:
            sys.stdout.write(f"[*] Article is already generated. Marking the job as 'completed'...\n")

            # update the k8 job status
            self.job.status = 'completed'
            self.job.save()

            # Mark the article internal link queue as completed
            internal_link_queue_items: QuerySet[ArticleInternalLinkQueue] = ArticleInternalLinkQueue.objects.filter(
                article=self.article, status__in=["processing", "queued"]
            )
            internal_link_queue_items.update(status="completed")
            return None

        if self.article.content:
            sys.stdout.write(f"[*] Article has content. Marking the job as 'completed'...\n")

            # update the k8 job status
            self.job.status = 'completed'
            self.job.save()

            # Mark the article as completed
            self.article.is_processing = False
            self.article.is_generated = True
            self.article.generated_on = timezone.now()
            self.article.save()

            # Mark the article internal link queue as failed
            internal_link_queue_items: QuerySet[ArticleInternalLinkQueue] = ArticleInternalLinkQueue.objects.filter(
                article=self.article, status__in=["processing", "queued"]
            )
            internal_link_queue_items.update(status="failed", error_message="Article is already generated. But might failed to get internal links...")

            logs = [log.message for log in self.job.kubernetesjoblogs_set.all()]
            logs.insert(0, "Article is already generated. But might failed to get internal links...")

            email_message: str = stuck_process_body_for_article_generation(self.job.user.email, self.job_id, logs) + "<br/>"
            self.email_messages.append(email_message)
            self.article_internal_link_queue_failed += 1

            return None

        # Mark the article generation task as failed
        self.article.is_processing = False
        self.article.is_failed = True
        self.article.save()

        logs = [log.message for log in self.job.kubernetesjoblogs_set.all()]
        email_message: str = stuck_process_body_for_article_generation(self.job.user.email, self.job_id, logs) + "<br/>"
        self.email_messages.append(email_message)
        self.k8_jobs_failed += 1
        self.article_generation_task_failed += 1

        # update the k8 job status
        self.job.status = 'failed'
        self.job.save()

        # Decrement user's article count
        user: User = self.article.user
        user.articles_generated -= 1
        user.total_articles_generated -= 1
        user.save()

    def website_scanning_task_stuck(self) -> bool:
        """
        Returns `True` if website scanning task is stuck, False otherwise.
        """
        # Get the current time
        now = timezone.now()

        # Calculate the threshold time (1 hour ago)
        threshold_time = now - datetime.timedelta(hours=1)

        # Check the latest last_scraped_on timestamp from WebPage
        latest_scraped: WebPage = self.website.webpage_set.all().order_by(
            '-last_scraped_on'
        ).values_list('last_scraped_on', flat=True).first()

        # If there is no webpage, treat `crawling_started_on` as `latest_scraped`
        if latest_scraped is None:
            latest_scraped = self.website.crawling_started_on

        # Return True if the latest last_scraped_on is older than the threshold time
        try:
            return latest_scraped < threshold_time
        except TypeError:
            return True

    def mark_website_scanning_task_as_failed_or_partial_completed(self):
        """
        Used to mark the website scanning task as failed or partial completed
        """
        if self.website.is_crawled or self.website.is_failed:
            sys.stdout.write(f"[*] {self.website.domain} is already marked as `{self.website.crawling_status}`. Marking the job as 'failed'...\n")

            self.website.is_crawling = False
            self.website.save()

            if self.job:
                # update the k8 job status
                self.job.status = 'failed'
                self.job.save()

            # Mark the schema_generating as false
            self.website.webpage_set.update(schema_generating=False)

            return None

        # Get the total number of website pages scanned
        webpage_count: int = self.website.webpage_set.count()

        if webpage_count == 0:
            # Mark the website scanning task as failed
            self.website.is_crawling = False
            self.website.is_crawled = False
            self.website.task_queued = False
            self.website.finding_sitemaps = False
            self.website.is_failed = True
            self.website.has_more_pages = True
            self.website.save()

            # Mark the website scanning queue as failed
            tasks: QuerySet[WebsiteScanQueue] = self.website.websitescanqueue_set.filter(status="processing")
            tasks.update(status="failed", error_message="Stuck in processing.")

            if self.job:
                # update the k8 job status
                self.job.status = 'failed'
                self.job.save()

            # Generate email message
            if self.job_id:
                email_message: str = stuck_process_body_for_website_scanning(self.website.user, self.website.domain, self.job_id) + "<br/>"
            else:
                email_message: str = stuck_process_body_for_website_scanning(self.website.user, self.website.domain, "website-scanning") + "<br/>"

            self.email_messages.append(email_message)
            self.k8_jobs_failed += 1
            self.website_scanning_task_failed += 1

        else:
            # Mark the website scanning task as completed
            self.website.is_crawling = False
            self.website.is_failed = False
            self.website.task_queued = False
            self.website.is_crawled = True
            self.website.has_more_pages = True
            self.website.crawling_ends_on = timezone.now()
            self.website.save()

            # Mark tasks as completed
            tasks: QuerySet[WebsiteScanQueue] = self.website.websitescanqueue_set.filter(status="processing")
            tasks.update(status="completed", completed_at=timezone.now())

            if self.job:
                # update the k8 job status
                self.job.status = 'completed'
                self.job.save()

    def handle(self, *args, **options):
        with transaction.atomic():
            reporting_time = {'hours': 1}
            time_format = list(reporting_time.keys())[0]
            duration = list(reporting_time.values())[0]
            filter_date = timezone.now() - datetime.timedelta(**reporting_time)

            # Get the current environment name
            environment = 'dev'  # default value
            db_name = os.environ.get('DB_NAME', '')
            if db_name == 'abun_staging_db':
                environment = 'staging'
            elif db_name == 'abun_production_db':
                environment = 'production'

            # fetch all jobs that are in running status for more than 'filter_date' time
            stuck_k8_jobs = KubernetesJob.objects.filter(created_on__lt=filter_date, status="running")

            # jobs stuck on K8
            for job in stuck_k8_jobs:
                self.job = job
                self.job_id = job.job_id

                sys.stdout.write(f"[*] {self.job.user.email} - '{self.job_id}' job stuck in processing.\n")

                if self.job_id.startswith("articlegeneration"):
                    # Fetch the article
                    try:
                        article = Article.objects.get(article_uid=self.job.metadata)
                        self.article = article

                        # Mark the article generation as failed or completed
                        self.mark_article_generation_as_failed_or_completed()

                    except Article.DoesNotExist:
                        sys.stdout.write(f"[*] No Article found with `{self.job.metadata}` UID. Marking the job as 'failed'...\n")

                        # update the k8 job status
                        self.job.status = 'failed'
                        self.job.save()

                elif self.job_id.startswith("internallink") or self.job_id.startswith("websitescanning"):
                    # Fetch the website
                    try:
                        self.website = Website.objects.get(domain=self.job.metadata)

                        if self.website.is_crawled:
                            sys.stdout.write(f"[*] Website scanning task for `{self.website.domain}` is completed. marking the job as `completed`...\n")

                            # Mark the job as completed
                            self.job.status = 'completed'
                            self.job.save()

                            # Mark the schema_generating as false
                            self.website.webpage_set.update(schema_generating=False)

                        elif self.website_scanning_task_stuck():
                            self.mark_website_scanning_task_as_failed_or_partial_completed()

                        else:
                            sys.stdout.write(f"[*] Website scanning task for `{self.website.domain}` is under processing. skipping...\n")

                    except Website.DoesNotExist:
                        sys.stdout.write(f"[*] No Website found with `{self.job.metadata}` domain. Marking the job as 'failed'...\n")

                        # update the k8 job status
                        self.job.status = 'failed'
                        self.job.save()

                elif self.job_id.startswith("competitorfinder"):
                    sys.stdout.write(f"[*] Competitor finder job failed. Marking the '{self.job_id}' job as 'failed'...\n")

                    logs = [log.message for log in self.job.kubernetesjoblogs_set.all()]
                    email_message: str = stuck_process_body_for_article_generation(self.job.user.email, self.job_id, logs) + "<br/>"
                    self.email_messages.append(email_message)

                    self.k8_jobs_failed += 1
                    self.competitor_finder_task_failed += 1

                    # update the k8 job status
                    self.job.status = 'failed'
                    self.job.save()

                elif self.job_id.startswith("articleinternallink"):
                    sys.stdout.write(f"[*] Article internal link job failed. Marking the '{self.job_id}' job as 'completed' because article is already generated...\n")

                    logs = [log.message for log in self.job.kubernetesjoblogs_set.all()]
                    logs.insert(0, "Article is already generated. But Failed to get internal links...")

                    email_message: str = stuck_process_body_for_article_generation(self.job.user.email, self.job_id, logs) + "<br/>"
                    self.email_messages.append(email_message)

                    # update the k8 job status
                    self.job.status = 'completed'
                    self.job.save()

                else:
                    sys.stdout.write(f"[*] Unknown job type. Marking the '{self.job_id}' job as 'failed'...\n")

                    logs = [log.message for log in self.job.kubernetesjoblogs_set.all()]
                    email_message: str = stuck_process_body_for_article_generation(self.job.user.email, self.job_id, logs) + "<br/>"
                    self.email_messages.append(email_message)

                    self.k8_jobs_failed += 1

                    # update the k8 job status
                    self.job.status = 'failed'
                    self.job.save()

            # Update the K8 jobs fields
            self.job = None
            self.job_id = None

            # Fetched the remaining websites
            stuck_websites = Website.objects.filter(is_crawling=True)

            for website in stuck_websites:
                self.website = website

                if self.website_scanning_task_stuck():
                    self.mark_website_scanning_task_as_failed_or_partial_completed()

            if self.email_messages:
                send_email(
                    ["<EMAIL>", AMIN_EMAIL, ADIL_EMAIL],
                    ABUN_NOTIFICATION_EMAIL,
                    "Abun - Stuck/Failed Process Notification",
                    f"Stuck Processes in last {duration} {time_format} on {environment}",
                    f"Total k8 Jobs stuck in processing: {self.k8_jobs_failed}<br/>" \
                    f"Total general article generation tasks failed: {self.article_generation_task_failed}<br/>" \
                    f"Total website scanning tasks failed: {self.website_scanning_task_failed}<br/>" \
                    f"Total competitor finder tasks failed: {self.competitor_finder_task_failed}<br/>" \
                    f"Total article internal link queue tasks failed: {self.article_internal_link_queue_failed}<br/>" \
                    + "<br/>".join(self.email_messages)
                )

                sys.stdout.write(f"[*] Email notification sent to admins.\n")

            else:
                sys.stdout.write(f"[*] No process is stuck/failed.\n")
