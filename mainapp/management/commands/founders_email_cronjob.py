"""
=============================================================================================================
This script will be run periodically using a cronjob to send founder's emails to users after their signup.
=============================================================================================================
"""

import datetime
import os
from datetime import timedelta
from zoneinfo import ZoneInfo

from django.core.management import BaseCommand
from django.db import transaction

from AbunDRFBackend.settings import AMIN_EMAIL
from mainapp.email_messages import founders_email_body
from mainapp.models import User
from mainapp.utils import send_email


class Command(BaseCommand):
    @transaction.atomic
    def handle(self, *args, **options):
        email_minutes = int(os.environ['FOUNDERS_EMAIL_AFTER_SIGNUP_MINUTES'])
        current_datetime = datetime.datetime.now(tz=ZoneInfo('UTC'))
        signup_cutoff_date = datetime.datetime(2023, 12, 27, tzinfo=ZoneInfo('UTC'))  # 27th Dec 2023

        # Read all users who have signed up after signup_cutoff_date but have not received the founders email
        for user in User.objects.filter(founders_email_sent=False, date_joined__gt=signup_cutoff_date):
            if current_datetime - user.date_joined >= timedelta(minutes=email_minutes):
                email_message: str = founders_email_body()
                send_email(user.email,
                           AMIN_EMAIL,
                           "Amin Memon",
                           "seo marketing tactic",
                           email_message,
                           reply_to=AMIN_EMAIL)
                user.founders_email_sent = True
                user.save()
