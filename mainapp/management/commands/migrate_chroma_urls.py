"""
=================================================================
Script to migrate existing ChromaDB collections to include URLs.
=================================================================
"""

import os
import json
import logging
import time
from datetime import datetime

from django.core.management import BaseCommand
from django.conf import settings

from mainapp.models import Website, WebPage
from mainapp.chroma_db_manager import ChromaDBManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(settings.BASE_DIR, 'chroma_migration.log'))
    ]
)
logger = logging.getLogger(__name__)

# Path for the progress tracking file
PROGRESS_FILE = os.path.join(settings.BASE_DIR, 'chroma_migration_progress.json')


class Command(BaseCommand):
    help = "Migrates existing ChromaDB collections to include URLs in separate collections with resume capability"

    def add_arguments(self, parser):
        parser.add_argument(
            "--website_id",
            type=int,
            help="Specific website ID to migrate (optional)",
        )
        parser.add_argument(
            "--reset",
            action="store_true",
            help="Reset progress tracking and start from the beginning",
        )
        parser.add_argument(
            "--batch_size",
            type=int,
            default=500,
            help="Number of pages to process in each batch (default: 500)",
        )
        parser.add_argument(
            "--max_retries",
            type=int,
            default=3,
            help="Maximum number of retries for failed operations (default: 3)",
        )

    def load_progress(self):
        """Load progress from the tracking file if it exists"""
        if os.path.exists(PROGRESS_FILE):
            try:
                with open(PROGRESS_FILE, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading progress file: {str(e)}")
                return {'websites': {}, 'last_updated': None}
        return {'websites': {}, 'last_updated': None}

    def save_progress(self, progress):
        """Save progress to the tracking file"""
        progress['last_updated'] = datetime.now().isoformat()
        try:
            with open(PROGRESS_FILE, 'w') as f:
                json.dump(progress, f)
        except Exception as e:
            logger.error(f"Error saving progress file: {str(e)}")

    def handle(self, *args, **options):
        website_id = options.get("website_id")
        reset = options.get("reset", False)
        batch_size = options.get("batch_size", 500)
        max_retries = options.get("max_retries", 3)

        # Load or reset progress
        if reset:
            progress = {'websites': {}, 'last_updated': None}
            logger.info("[*] Resetting progress tracking")
            self.stdout.write("[*] Resetting progress tracking")
        else:
            progress = self.load_progress()
            if progress['last_updated']:
                logger.info(f"[*] Resuming from previous run ({progress['last_updated']})")
                self.stdout.write(f"[*] Resuming from previous run ({progress['last_updated']})")

        # Initialize ChromaDB manager
        chromaDBManager = ChromaDBManager()

        # Get websites to process
        if website_id:
            websites = Website.objects.filter(id=website_id, is_crawled=True)
            logger.info(f"[*] Processing specific website ID: {website_id}")
            self.stdout.write(f"[*] Processing specific website ID: {website_id}")
        else:
            websites = Website.objects.filter(is_crawled=True)
            logger.info(f"[*] Processing all crawled websites: {websites.count()} websites found")
            self.stdout.write(f"[*] Processing all crawled websites: {websites.count()} websites found")

        # Process each website
        for website in websites.iterator():
            website_str_id = str(website.id)

            # Skip if website is already completed
            if website_str_id in progress['websites'] and progress['websites'][website_str_id]['completed']:
                logger.info(f"[*] Skipping already completed website: {website.domain} (ID: {website.id})")
                self.stdout.write(f"[*] Skipping already completed website: {website.domain} (ID: {website.id})")
                continue

            # Initialize website progress if not exists
            if website_str_id not in progress['websites']:
                progress['websites'][website_str_id] = {
                    'domain': website.domain,
                    'completed': False,
                    'processed_pages': [],
                    'total_pages': 0,
                    'last_page_index': 0
                }

            logger.info(f"[*] Migrating ChromaDB collections for {website.domain} (ID: {website.id})")
            self.stdout.write(f"[*] Migrating ChromaDB collections for {website.domain} (ID: {website.id})")

            # Get all webpages for this website
            webpages = WebPage.objects.filter(website=website)

            if not webpages.exists():
                logger.info(f"[*] No webpages found for {website.domain}, skipping")
                self.stdout.write(f"[*] No webpages found for {website.domain}, skipping")
                progress['websites'][website_str_id]['completed'] = True
                self.save_progress(progress)
                continue

            # Update total pages count
            total_pages = webpages.count()
            progress['websites'][website_str_id]['total_pages'] = total_pages

            logger.info(f"[*] Creating URL collection for {website.domain} with {total_pages} pages")
            self.stdout.write(f"[*] Creating URL collection for {website.domain} with {total_pages} pages")

            # Get the last processed page index
            last_page_index = progress['websites'][website_str_id]['last_page_index']
            processed_pages = set(progress['websites'][website_str_id]['processed_pages'])

            # Process pages in batches, starting from the last index
            for i in range(last_page_index, total_pages, batch_size):
                batch = webpages[i:i+batch_size]

                # Add URLs to the URL collection
                for j, webpage in enumerate(batch):
                    current_index = i + j

                    # Skip if already processed
                    if webpage.id in processed_pages:
                        continue

                    # Skip pages without embedding_id
                    if not webpage.embedding_id:
                        processed_pages.add(webpage.id)
                        progress['websites'][website_str_id]['processed_pages'] = list(processed_pages)
                        continue

                    # Try to add to URLs collection with retries
                    retry_count = 0
                    success = False

                    while retry_count < max_retries and not success:
                        try:
                            # Add to URLs collection using the same embedding_id
                            chromaDBManager._add_to_collection(
                                webpage=webpage,
                                chroma_id=webpage.embedding_id,
                                collection_type="urls",
                                document=webpage.url
                            )
                            success = True
                            processed_pages.add(webpage.id)

                        except Exception as e:
                            retry_count += 1
                            error_msg = f"[!] Error processing page {webpage.id} (attempt {retry_count}/{max_retries}): {str(e)}"
                            logger.error(error_msg)
                            self.stdout.write(error_msg)

                            if retry_count < max_retries:
                                # Exponential backoff
                                wait_time = 2 ** retry_count
                                logger.info(f"[*] Waiting {wait_time} seconds before retrying...")
                                self.stdout.write(f"[*] Waiting {wait_time} seconds before retrying...")
                                time.sleep(wait_time)

                    # Update progress after each page
                    progress['websites'][website_str_id]['processed_pages'] = list(processed_pages)
                    progress['websites'][website_str_id]['last_page_index'] = current_index

                    # Save progress periodically
                    if len(processed_pages) % 100 == 0:
                        self.save_progress(progress)
                        progress_msg = f"[*] Processed {len(processed_pages)}/{total_pages} pages ({(len(processed_pages)/total_pages)*100:.1f}%)"
                        logger.info(progress_msg)
                        self.stdout.write(progress_msg)

                # Save progress after each batch
                self.save_progress(progress)

            # Mark website as completed
            progress['websites'][website_str_id]['completed'] = True
            self.save_progress(progress)

            completion_msg = f"[*] Successfully migrated {len(processed_pages)} pages for {website.domain}"
            logger.info(completion_msg)
            self.stdout.write(completion_msg)

        final_msg = "[*] Migration completed successfully"
        logger.info(final_msg)
        self.stdout.write(final_msg)
