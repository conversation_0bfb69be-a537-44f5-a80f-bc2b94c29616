"""
====================================================================================================================
This script will be run periodcally using cronjob to capture current Abun stats for Admin page.
====================================================================================================================
"""
import datetime
from zoneinfo import ZoneInfo

from django.core.management import BaseCommand
from django.db.models import Q

from mainapp.models import User, AdminStats
from mainapp.stripe_utils import get_stripe_product_data_by_name


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Fetch the free plan (aka. trial plan) product id
        trial_plan_product_id: str = get_stripe_product_data_by_name("Trial")['id']

        # Get current date
        current_date: datetime.date = datetime.datetime.now(tz=ZoneInfo('UTC')).date()

        # Get required stats
        total_signups: int = User.objects.count()
        paid_user_count: int = User.objects\
            .exclude(Q(stripe_product_id__isnull=True) | Q(stripe_product_id=trial_plan_product_id))\
            .count()
        free_user_count: int = total_signups - paid_user_count
        signups_today: int = User.objects.filter(date_joined__date=current_date).count()
        paid_users_today: int = User.objects\
            .exclude(Q(stripe_product_id__isnull=True) | Q(stripe_product_id=trial_plan_product_id))\
            .filter(date_joined__date=current_date)\
            .count()
        paid_users_weekly: int = User.objects\
            .exclude(Q(stripe_product_id__isnull=True) | Q(stripe_product_id=trial_plan_product_id))\
            .filter(date_joined__date__gte=current_date - datetime.timedelta(days=7))\
            .count()
        
        self.stdout.write(f"> total_signups: {total_signups}")
        self.stdout.write(f"> paid_user_count: {paid_user_count}")
        self.stdout.write(f"> free_user_count: {free_user_count}")
        self.stdout.write(f"> signups_today: {signups_today}")
        self.stdout.write(f"> paid_users_today: {paid_users_today}")
        self.stdout.write(f"> paid_users_this_week: {paid_users_weekly}")

        stats = AdminStats(
            total_signups=total_signups,
            total_paid_customer=paid_user_count,
            total_free_customers=free_user_count,
            today_signup=signups_today,
            today_paid_users=paid_users_today,
            paid_users_weekly=paid_users_weekly
        )
        stats.save()

        self.stdout.write("[*] Stats save successfully!")
