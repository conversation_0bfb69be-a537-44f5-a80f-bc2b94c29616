"""
====================================================================================================================
This script will be run periodcally using cronjob to find all the ScheduleArticlePosting articles that need to be published
using user's current integration. Additionally, once the article in user's ScheduleArticlePosting set is published, it
deletes the current ScheduleArticlePosting object.
====================================================================================================================
"""
import datetime
import logging
from zoneinfo import ZoneInfo
from typing import Dict

import requests
from django.core.management import BaseCommand

from mainapp.models import ScheduleArticlePosting, User, Article
from mainapp.utils import (publish_article_to_wp, publish_article_to_wf, publish_article_to_wix, publish_article_to_shopify)


logger = logging.getLogger(__file__)

class Command(BaseCommand):
    def handle(self, *args, **options):
        current_datetime = datetime.datetime.now(tz=ZoneInfo('UTC'))
        schedule_articles = ScheduleArticlePosting.objects.filter(schedule_datetime__lte=current_datetime)

        for schedule_article in schedule_articles:
            article: Article = schedule_article.article
            user: User = article.user

            if schedule_article.selected_integration_name is None:
                self.stdout.write(f"[*] No integrtation is selected for '{article.article_uid}' article posting. Skipping...")
                schedule_article.delete()
                continue

            if not any([user.wordpress_integrations.exists(), user.webflow_integrations.exists(),
                        user.wix_integrations.exists(), user.shopify_integrations.exists()]):
                self.stdout.write(f"[*] '{user.email}' not have any integration. Skipping...")
                schedule_article.delete()
                continue

            if not article.is_generated:
                self.stdout.write(f"[*] '{article.article_uid}' is not generated. Skipping...")
                schedule_article.delete()
                continue

            if article.is_posted:
                self.stdout.write(f"[*] '{article.article_uid}' is alredy posted. Skipping...")
                schedule_article.delete()
                continue

            # Publish article to user site
            if "wordpress" in schedule_article.selected_integration_name and user.wordpress_integrations.exists():
                self.stdout.write(f"[*] Posting '{article.article_uid}' article to wordpress...")

                res = publish_article_to_wp(article, user,
                                            wp_site_url=schedule_article.selected_integration_unique_text_id,
                                            status=schedule_article.post_status)

                if res["status"] == "success":
                    self.stdout.write(f"[*] {article.article_uid} was posted successfully!")
                else:
                    logger.critical(f"Failed to post {article.article_uid} to wordpress, Error message {res['error_message']}")

            elif "webflow" in schedule_article.selected_integration_name and user.webflow_integrations.exists():
                self.stdout.write(f"[*] Posting '{article.article_uid}' article to webflow...")

                res = publish_article_to_wf(article, article.user,
                                             schedule_article.selected_integration_unique_text_id,
                                             status=schedule_article.post_status)

                if res and res["status"] == "success":
                    self.stdout.write(f"[*] {article.article_uid} was posted successfully!")
                elif res and res.get("error_message"):
                    logger.critical(f"{res['error_message']}")
                else:
                    logger.critical(f"Failed to post {article.article_uid}")

            elif "wix" in schedule_article.selected_integration_name and user.wix_integrations.exists():
                self.stdout.write(f"[*] Posting '{article.article_uid}' article to wix...")

                # Publish the article to wix
                res = publish_article_to_wix(article, site_id=schedule_article.selected_integration_unique_text_id,
                                             status=schedule_article.post_status)

                if res["status"] == "success":
                    self.stdout.write(f"[*] {article.article_uid} was posted successfully!")
                else:
                    logger.critical(f"Failed to post {article.article_uid}. Error Message: {res['error_message']}")
            
            elif "shopify" in schedule_article.selected_integration_name and user.shopify_integrations.exists():
                self.stdout.write(f"[*] Posting '{article.article_uid}' article to shopify...")

                # Publish the article to shopify
                res = publish_article_to_shopify(article, status=schedule_article.post_status, shop_url=schedule_article.selected_integration_unique_text_id)

                if res["status"] == "success":
                    self.stdout.write(f"[*] {article.article_uid} was posted successfully!")
                else:
                    logger.critical(f"Failed to post {article.article_uid}. Error Message: {res['error_message']}")

            else:
                logger.critical(f"Could not post {article.article_uid} article to user's website due to bad integration")

            # Delete the scheduled article object
            schedule_article.delete()
