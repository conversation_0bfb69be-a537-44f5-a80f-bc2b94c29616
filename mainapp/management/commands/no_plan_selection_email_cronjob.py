"""
====================================================================================================================
This script will be run periodically using cronjob to send email notification if user hasn't selected any plan within
2 hours after signup
====================================================================================================================
"""
from typing import Any, List
from concurrent.futures import ThreadPoolExecutor

from django.utils import timezone
from django.db.models import QuerySet
from django.core.management import BaseCommand

from mainapp.models import User
from mainapp.utils import send_email
from AbunDRFBackend.settings import AMIN_EMAIL
from mainapp.email_messages import no_plan_selection_email_body


class Command(BaseCommand):
    email_body: str = no_plan_selection_email_body()

    def handle(self, *args: Any, **options: Any) -> None:
        today_datetime = timezone.now()
        users_email_notifier: List[User] = []

        users: QuerySet[User] = User.objects.filter(
            stripe_subscription_id=None,
            no_plan_selection_email_sent=False
        )

        for user in users:
            difference_in_hours = (today_datetime - user.date_joined).total_seconds() // 3600

            if difference_in_hours > 24:
                user.no_plan_selection_email_sent = True
                user.save()
                continue

            if difference_in_hours >= 2:
                self.stdout.write(f"[*] {user.email} has not chosen a plan yet, adding user to 'users_email_notifier'")
                users_email_notifier.append(user)

        if users_email_notifier:
            for user in users_email_notifier:
                message = self.send_no_plan_selection_email(user)
                self.stdout.write(message)

    def send_no_plan_selection_email(self, user: User) -> str:
        # update user no plan selection field
        user.no_plan_selection_email_sent = True
        user.save()

        send_email(
            user.email,
            AMIN_EMAIL,
            "Amin Memon",
            "abun / no plan",
            self.email_body,
            reply_to=AMIN_EMAIL
        )

        return f"[*] No plan selection email sent to {user.email} user."
