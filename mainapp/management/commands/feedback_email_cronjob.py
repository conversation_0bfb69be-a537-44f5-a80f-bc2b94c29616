"""
===================================================================================================
This script will be run periodically using a cronjob to send emails to users asking for feedback.
===================================================================================================
"""

import datetime
import os
from datetime import timedelta
from zoneinfo import ZoneInfo

from django.core.management import BaseCommand
from django.db import transaction

from AbunDRFBackend.settings import JD_EMAIL
from mainapp.email_messages import ask_for_feedback_email_body
from mainapp.models import User
from mainapp.utils import send_email


class Command(BaseCommand):
    @transaction.atomic
    def handle(self, *args, **options):
        email_hours = int(os.environ['FEEDBACK_EMAIL_HOURS'])
        
        # Read all users who have connected their website but have not received feedback email yet.
        for user in User.objects.filter(current_active_website__isnull=False, feedback_email_sent=False):
            current_datetime = datetime.datetime.now(tz=ZoneInfo('UTC'))
            if current_datetime - user.date_joined >= timedelta(hours=email_hours):
                email_message: str = ask_for_feedback_email_body(user.username)
                send_email(user.email,
                           JD_EMAIL,
                           "Junaid Ansari",
                           "feedback",
                           email_message)
                user.feedback_email_sent = True
                user.save()
