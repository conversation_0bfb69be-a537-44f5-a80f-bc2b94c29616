"""
====================================================================================================================
This script will be run periodcally using cronjob to fetch and store all the indexation details for a website.
====================================================================================================================
"""
import itertools
from typing import Dict, List, Tuple
from concurrent.futures import ThreadPoolExecutor
from urllib3.exceptions import SSLError, MaxRetryError, ConnectionError
import datetime
from zoneinfo import ZoneInfo
from django.utils.timezone import now, timedelta

from django.core.management import BaseCommand
from django.db.models import QuerySet
from django.db.models import Exists, OuterRef

from google.oauth2.credentials import Credentials
from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError

from mainapp.models import User, WebsiteIndexation, Website, Article, GoogleIntegration
from mainapp.quickindex import sitemaps as fetch_sitemaps
from mainapp.email_messages import website_indexation_process_completed_body
# from mainapp.utils import send_email
from AbunDRFBackend.settings import logging, ABUN_NOTIFICATION_EMAIL
from mainapp.google_integration_utils import (get_google_oauth2_credentials, get_site_name_on_gsc,
                                              fetch_sitemap_information, fetch_url_inspection_details)
from mainapp.quickindex.indexing import index_url

logger = logging.getLogger(__file__)


class Command(BaseCommand):
    QPD = 200  # queries per day for URL inspection (calls querying the same site)
    QPM = 600   # queries per minute for URL inspection (calls querying the same site)

    def fetch_website_urls(self, credentials: Credentials, site: str) -> List:
        """
        Fetches all the URLs of the website via sitemap.
        Uses Search Console sitemaps if available; falls back to default sitemap.xml.
        :param credentials: Google oauth2 credentials
        :param site: site name on GSC (e.g., 'sc-domain:creatiwise.com')
        """

        self.stdout.write("[*] Fetching URLs\n")

        try:
            res = fetch_sitemap_information(credentials, site)
            sitemaps = res.get('sitemap', [])
        except Exception as e:
            # self.stdout.write(f"[!] Error fetching GSC sitemap: {e}")
            sitemaps = []

        all_urls = []

        if sitemaps:
            for sitemap in sitemaps:
                sitemap_url = sitemap.get('path')
                urls = fetch_sitemaps.fetch_urls_from_sitemap_recursive(sitemap_url)
                all_urls.extend(urls)
        else:
            logger.info("[*] No sitemaps found in GSC, trying default sitemap.xml\n")
            # Construct default sitemap.xml URL
            domain = site.replace("sc-domain:", "").replace("https://", "").replace("http://", "").strip("/")
            default_sitemap_url = f"https://{domain}/sitemap.xml"

            try:
                urls = fetch_sitemaps.fetch_urls_from_sitemap_recursive(default_sitemap_url)
                all_urls.extend(urls)
            except Exception as e:
                logger.info(f"[!] Failed to fetch default sitemap: {e}")

        # Deduplicate URLs
        unique_urls = list(set(all_urls))    
        return unique_urls

    def should_index(self, page: dict) -> bool:
        # If it's not indexed AND
        #    either not sent at all, or status is not completed
        if page.get('indexed', False) or page.get('status') == "Index Completed":
            return False

        if page.get('sent_for_indexing', False) and page.get('status') == "Request Sent for Indexing":
            return False

        return True

    def handle(self, *args, **options):
        users = User.objects.filter(
            Exists(
                GoogleIntegration.objects.filter(website__user=OuterRef('pk'))
            )
        )

        for user in users:
            self.stdout.write(f"[*] Running for '{user.email}'\n")

            try:
                credentials: Credentials = get_google_oauth2_credentials(user, "google-search-console")
            except RefreshError:
                self.stdout.write("[X] Access has been revoked by the user or the token has expired. Skipping...\n")
                continue

            if not credentials:
                self.stdout.write("[X] Error while fetching google oauth2 credentials. Skipping...\n")
                continue

            self.stdout.write("[*] Google oauth2 credentials fetched!\n")

            websites: QuerySet[Website] = user.website_set.all()
            for website in websites:
                auto_indexing = website.auto_indexing
                # indexed_allowed = website.indexed_allowed
                # if indexed_allowed:
                try:
                    site = get_site_name_on_gsc(credentials, website.domain)
                    website_indexation, _ = WebsiteIndexation.objects.get_or_create(user=user, website=website, search_engine='google')
                    
                    if auto_indexing:
                        last_indexed = website.last_auto_indexed_on
                        if last_indexed and (now() - last_indexed) < timedelta(hours=24):
                            self.stdout.write(f"[~] Skipping '{website.domain}' - already indexed in the last 24h.\n")
                            continue
                        
                        self.stdout.write(f"[*] Auto-indexing enabled for '{website.domain}'\n")

                        # Fetch all URLs from sitemap
                        sitemap_urls = self.fetch_website_urls(credentials, site)

                        # Get URLs already stored
                        indexed_urls = {page['url']: page for page in website_indexation.indexation_details or []}                            

                        # Only keep URLs not already in indexation details
                        urls_to_index = [
                            url for url in sitemap_urls
                            if url not in indexed_urls or self.should_index(indexed_urls[url])
                        ]


                        if not urls_to_index:
                            self.stdout.write(f"[*] No new URLs found for indexing for '{website.domain}'\n")
                            continue

                        self.stdout.write(f"[*] Found {len(urls_to_index)} new URLs for indexing.\n")

                        # Limit to 200 per day
                        urls_to_index = urls_to_index[:self.QPD]
                        indexed_now = []

                        for url in urls_to_index:
                            try:
                                response = index_url(credentials, site, url)
                                if response.status_code == 200:
                                    self.stdout.write(f"[✓] Indexed: {url}")
                                    indexed_now.append({
                                        'url': url,
                                        'indexed': False,
                                        'sent_for_indexing': True,
                                        'sent_on': datetime.datetime.now(tz=ZoneInfo('UTC')).isoformat(),
                                        'status': "Request Sent for Indexing",
                                        'coverage_state': "",
                                        'last_crawled': "",
                                    })
                                else:
                                    self.stdout.write(f"[X] Failed to index: {url} -> Status: {response.status_code}")
                            except Exception as e:
                                self.stdout.write(f"[X] Exception while indexing {url}: {e}")

                        # Append to existing indexation details
                        if indexed_now:                            
                            if not website_indexation.indexation_details:
                                website_indexation.indexation_details = []
                                
                            website_indexation.indexation_details += indexed_now
                            website_indexation.urls_sent_for_indexing = len(indexed_now)
                            website_indexation.save()
                            self.stdout.write(f"[*] Indexed {len(indexed_now)} URLs and updated indexation details.\n")
                            
                            website.last_auto_indexed_on = now()
                            website.save()                                
                    # else:
                    #     self.stdout.write(f"[*] Done. {self.QPD} pages index data stored!\n")

                except HttpError as err:
                    reason = err._get_reason()
                    self.stdout.write(f"[X] HttpError -> '{reason}', Skipping...\n")
                    logger.error(f"HttpError: {reason}")
                
                except SSLError as err:
                    self.stdout.write(f"[X] SSLError -> '{err}', Skipping...\n")
                    logger.error(err)
                
                except MaxRetryError as err:
                    self.stdout.write(f"[X] MaxRetryError -> '{err}', Skipping...\n")
                    logger.error(err)

                except ConnectionError as err:
                    self.stdout.write(f"[X] ConnectionError -> '{err}', Skipping...\n")
                    logger.error(err)
                
                except Exception as err:
                    self.stdout.write(f"[X] Something went wrong! Skipping...\n")
                    logger.critical(err)
                # else: 
                #     self.stdout.write(f"[X] Indexing is not allowed for this connected gsc. Skipping...\n")
                #     logger.error("Indexing is not allowed for this connected gsc. Skipping...")
