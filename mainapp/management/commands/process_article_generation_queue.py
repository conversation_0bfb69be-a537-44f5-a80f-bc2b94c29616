import json
import time
import logging
from typing import List, Dict
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed

import requests
from django.urls import reverse
from django.utils import timezone
from django.db.models import QuerySet
from django.core.management.base import BaseCommand

from mainapp.tasks import celery_check_flyio_provisioning
from mainapp.utils import generate_k8_job_id, get_redis_connection
from mainapp.models import ArticleGenerationQueue, Article, User, KubernetesJob
from AbunDRFBackend.settings import (FLY_API_HOST, FLY_ARTICLE_GEN_APP_NAME, FLY_ARTICLE_GEN_DEPLOY_TOKEN, FLY_ARTICLE_GEN_IMAGE_URL,
                                     REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY, K8_JOB_RETRIES)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Process queued article generation tasks'
    DELAY = 60  # Delay between processing batches (in seconds)
    BATCH_SIZE = 50  # Number of articles to process in each batch
    MAX_CONCURRENT_MACHINES = 200  # Maximum number of concurrent Fly.io machines

    def handle(self, *args, **kwargs):
        self.stdout.write('Starting article generation queue processing...')

        try:
            self.process_queue()
        except Exception as e:
            logger.critical(f"Error processing article generation queue: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error processing article generation queue: {str(e)}'))

    def get_running_machines(self) -> List[Dict]:
        """
        Get a list of currently running machines on Fly.io
        """
        try:
            response = requests.get(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_GEN_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_GEN_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                }
            )

            if response.status_code == 200:
                machines = response.json()
                # Filter for running machines only
                running_machines = [m for m in machines if m.get('state') == 'started']
                return running_machines
            else:
                logger.error(f"Failed to get running machines: {response.text}")
                return []
        except Exception as e:
            logger.error(f"Error getting running machines: {str(e)}")
            return []

    def submit_task_to_flyio(self, queue_item: ArticleGenerationQueue) -> bool:
        """
        Submit a task to Fly.io
        """
        # Update queue item status to 'processing'
        queue_item.status = 'processing'
        queue_item.save()

        # Get article and user objects
        article: Article = queue_item.article
        user: User = queue_item.user

        if article.is_generated and not queue_item.regenerate:
            logger.info(f"Article {article.article_uid} is already generated. Skipping...")
            queue_item.status = 'completed'
            queue_item.submitted_at = timezone.now()
            queue_item.save()
            return True

        try:

            # ------------------- Create K8 Job -------------------
            art_gen_job_id = generate_k8_job_id('articlegeneration', username=user.username)
            art_gen_data = {
                'article_uid': article.article_uid,
                'domain': user.current_active_website.domain if user.current_active_website else "no website connected",
                'title': article.title,
                'image_source': user.image_source,
                'abun_webhook_url': reverse('wh-k8-article-generation'),
                'article_tone_of_voice': user.article_tone_of_voice,
                'external_backlinks_preference': user.external_backlinks_preference,
                'article_language_preference': user.article_language_preference,
                'tone_of_article': user.tone_of_article,
                'scale_of_tone': user.scale_of_tone,
                'max_internal_backlinks': user.max_internal_backlinks,
                'max_external_backlinks': user.max_external_backlinks,
                'ai_generated_image_style': user.ai_generated_image_style,
                'other_top_ranking_urls': article.other_top_ranking_urls,
                'article_context': article.context,
                'use_deepinfra_for_ai_img_gen': user.feature_image_template_label == "premium" and False or True,
            }

            if article is not None and article.keyword is not None:
                art_gen_data['keyword'] = article.keyword.keyword
            else:
                art_gen_data['keyword'] = None

            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.set(art_gen_job_id, json.dumps(art_gen_data))
                redis_connection.expire(art_gen_job_id, REDIS_ART_GEN_EXPIRY)

            art_gen_k8_job = KubernetesJob(
                job_id=art_gen_job_id,
                user=user,
                status='running',
                metadata=article.article_uid,
            )
            art_gen_k8_job.save()

            # Create Fly.io machine to process the article
            cmd = f"python3 article_gen.py {art_gen_job_id}"
            cmd = cmd.split()
            worker_props = {
                "config": {
                    "image": FLY_ARTICLE_GEN_IMAGE_URL,
                    "auto_destroy": True,
                    "init": {
                        "cmd": cmd
                    },
                    "restart": {
                        "policy": "on-failure",
                        "max_retries": K8_JOB_RETRIES
                    },
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 1,
                        "memory_mb": 1024
                    }
                },
            }

            res = requests.post(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_GEN_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_GEN_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                json=worker_props
            )

            if res.status_code != 200:
                logger.error(f"Failed to send article generation task to Fly.io: {res.text}")
                queue_item.error_message = f"Failed to send task to Fly.io: {res.text}"
                queue_item.save()
                return False

            # Get machine ID and update queue item
            machine_id = res.json()['id']

            # Run the celery flyio provisioning task directly
            celery_check_flyio_provisioning.run(machine_id,
                                                art_gen_job_id,
                                                FLY_ARTICLE_GEN_APP_NAME,
                                                FLY_ARTICLE_GEN_DEPLOY_TOKEN)

            queue_item.status = 'completed'
            queue_item.submitted_at = timezone.now()
            queue_item.save()

            logger.info(f"Article generation task {article.article_uid} submitted to Fly.io machine {machine_id}")
            self.stdout.write(self.style.SUCCESS(f"Task {article.article_uid} submitted to machine {machine_id}"))

            return True

        except Exception as e:
            logger.critical(f"Error submitting task {article.article_uid} to Fly.io: {str(e)}")

            # Mark the task as failed
            queue_item.status = 'failed'
            queue_item.error_message = f"Error submitting task to Fly.io: {str(e)}"
            queue_item.save()

            if not queue_item.regenerate:
                # Decrement user's article count
                user.articles_generated -= 1
                user.total_articles_generated -= 1

                # Update article status
                article.is_processing = False
                article.is_failed = True
                article.save()

            return False

    def process_queue(self) -> None:
        """
        Process the article generation queue in batches
        """
        while True:
            # Get batch of queued tasks
            queued_tasks: QuerySet[ArticleGenerationQueue] = ArticleGenerationQueue.objects.filter(
                status='queued'
            ).order_by('created_at')[:self.BATCH_SIZE]

            if not queued_tasks:
                # If no tasks are queued, wait for a specified delay before checking again
                self.stdout.write(f'No queued tasks found. Waiting for {self.DELAY} seconds...')
                time.sleep(self.DELAY)
                continue

            # Check how many machines are currently running
            running_machines = self.get_running_machines()
            available_slots = max(0, self.MAX_CONCURRENT_MACHINES - len(running_machines))

            if available_slots <= 0:
                self.stdout.write(f'Maximum number of concurrent machines ({self.MAX_CONCURRENT_MACHINES}) reached. Waiting for {self.DELAY} seconds...')
                time.sleep(self.DELAY)
                continue

            # Process up to available_slots tasks
            tasks_to_process = queued_tasks[:available_slots]
            self.stdout.write(f'Processing {len(tasks_to_process)} tasks out of {queued_tasks.count()} queued tasks')

            with ThreadPoolExecutor(max_workers=available_slots) as executor:
                # Submit tasks to Fly.io in parallel
                future_to_task = {executor.submit(self.submit_task_to_flyio, task): task for task in tasks_to_process}

                for future in as_completed(future_to_task):
                    task: ArticleGenerationQueue = future_to_task[future]
                    user: User = task.user
                    article: Article = task.article

                    try:
                        success = future.result()
                        if not success:
                            self.stdout.write(self.style.ERROR(f'Failed to submit task {article.article_uid} to Fly.io'))
                    except Exception as e:
                        logger.critical(f"Error processing task {article.article_uid}: {str(e)}")
                        self.stdout.write(self.style.ERROR(f'Error processing task {article.article_uid}: {str(e)}'))

                        # Mark the task as failed
                        task.status = 'failed'
                        task.error_message = f"Error processing task: {str(e)}"
                        task.save()

                        if not task.regenerate:
                            # Decrement user's article count
                            user.articles_generated -= 1
                            user.total_articles_generated -= 1

                            # Update article status
                            article.is_processing = False
                            article.is_failed = True
                            article.save()

            # Wait before processing the next batch
            time.sleep(self.DELAY)
