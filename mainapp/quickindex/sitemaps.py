import logging
from typing import List

import requests
from bs4 import BeautifulSoup

logger = logging.getLogger(__file__)


def fetch_urls_from_sitemap(sitemap_url: str) -> List[str]:
    """
    Fetch the URls from sitemap
    :param sitemap_url: URL of the sitemap
    """
    response = requests.get(sitemap_url)
    if response.status_code == 200:
        soup = BeautifulSoup(response.text, "xml")
        urls = [loc.text for loc in soup.find_all("loc")]
        return urls
    else:
        logger.error(f"Failed to fetch sitemap: {sitemap_url}")
        return []


def fetch_urls_from_sitemap_recursive(sitemap_url: str, visited_sitemaps: set = set()) -> List[str]:
    """
    Fetch the URLs from sitemap recursively
    :param sitemap_url: URL of the sitemap
    :param visited_sitemaps: Set of already visited sitemaps
    """
    visited_sitemaps.add(sitemap_url)
    urls = fetch_urls_from_sitemap(sitemap_url)

    ALL_URLS = []

    for url in urls:
        if not url.endswith(".xml"):
            ALL_URLS.append(url)

        # if the URL points to another sitemap, recursively fetch URLs from it
        if url.endswith(".xml") and url not in visited_sitemaps:
            return fetch_urls_from_sitemap_recursive(url, visited_sitemaps)

    return ALL_URLS
