import logging
from typing import <PERSON>ple

from google.oauth2.credentials import Credentials
from googleapiclient.errors import HttpError

from mainapp.google_integration_utils import build_api_service


logger = logging.getLogger(__file__)


def index_url(url: str, credentials: Credentials) -> Tuple[bool, str | None]:
    """
    Submit a url to google search console for indexing
    :param url: Page url to index
    :param credentials: Google oauth2 credentials
    """
    try:
        service = build_api_service('indexing', 'v3', credentials)
        url_object = {
            'url': url,
            'type': "URL_UPDATED"
        }

        # submit the url for indexing
        response = service.urlNotifications().publish(body=url_object).execute()
        logger.debug(response)
        return True, None

    except HttpError as err:
        reason = err._get_reason()
        logger.error(reason)
        return False, reason

    except Exception as err:
        logger.critical(err)
        return False, "Server Error"
