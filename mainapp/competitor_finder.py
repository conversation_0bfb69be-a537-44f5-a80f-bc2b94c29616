import os
import json
import html
import logging
from typing import List

import redis
import requests
import tldextract

from django.urls import reverse

from AbunDRFBackend.settings import (REDIS_HOST, REDIS_PORT, REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY, FLY_API_HOST,
                                     FLY_COMPETITOR_FINDER_DEPLOY_TOKEN, FLY_COMPETITOR_FINDER_APP_NAME, FLY_COMPETITOR_FINDER_GEN_IMAGE_URL)

from mainapp.models import KubernetesJob, User, IgnoredCompetitor, Competitor
from mainapp.utils import create_k8_job, generate_k8_job_id, get_redis_connection
from mainapp.tasks import celery_check_flyio_provisioning, celery_save_logos

# Set up logger
logger = logging.getLogger(__name__)

class CompetitorFinder:
    def __init__(self,
                 user: User,
                 domain: str,
                 limit: str,
                 regenerate_competitor: bool = False,
                 k8_job_id: str | None = None,
                 ) -> None:
        self.user = user
        self.domain = domain
        self.limit = limit
        self.regenerate_competitor = regenerate_competitor        
        self.competitor_finder_job_id : str | None = k8_job_id
        if self.competitor_finder_job_id:
            self.k8_job = KubernetesJob.objects.get(job_id=self.competitor_finder_job_id)
        else:
            self.k8_job = None

    def store_competitors(self, domains: List):
        """
        Store the competitors
        :param domains: List of domains
        """
        website = self.user.current_active_website
        competitors_to_ignore: List[str] = list(IgnoredCompetitor.objects.all().values_list('domain', flat=True))

        # clean each domain and ignore bad ones
        cleaned_domains: List[str] = []
        for domain in domains:
            domain_data = tldextract.extract(html.escape(domain))
            domain = domain_data.domain
            suffix = domain_data.suffix

            if suffix and domain and (domain not in competitors_to_ignore):
                cleaned_domains.append(f"{domain}.{suffix}")

        # # remove duplicates        
        # Remove competitors that are already present.
        cleaned_domains = list(
            set(cleaned_domains) - set(list(website.competitor_set.all().values_list('domain', flat=True)))
        )

        # fetch and store logos for these domains
        celery_save_logos.delay(cleaned_domains)

        # Create and add the competitors
        for domain in cleaned_domains:
            comp = Competitor(
                website=website,
                domain=domain,
                protocol="https",
                logo_url=f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}/{os.environ['CLOUDFLARE_R2_ENV']}/logo/{domain}"
            )
            comp.save()

        self.k8_job.status = "completed"
        self.k8_job.save()
        
        if self.regenerate_competitor:
            website.regenerate_competitors = self.regenerate_competitor
            website.save()

        # Delete the redis key
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.delete(self.k8_job.job_id)

        logger.error(f"job_di ---- {self.k8_job}")

    def create_competitor_finder_task_on_k8s(self):
        """
        Creates competitor finder task on K8s
        """
        competitor_finder_job_id = generate_k8_job_id('competitorfinder', username=self.user.username)
        self.competitor_finder_job_id = competitor_finder_job_id

        competitor_finder_data = {
            'domain': self.domain,
            'limit': self.limit,
            'regenerate_competitor': self.regenerate_competitor,
            'abun_webhook_url': reverse('wh-k8-competitor-finder'),        
        }

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(self.competitor_finder_job_id, json.dumps(competitor_finder_data))
            redis_connection.expire(self.competitor_finder_job_id, REDIS_ART_GEN_EXPIRY)

        competitor_finder_gen_k8_job = KubernetesJob(
            job_id=self.competitor_finder_job_id,
            user=self.user,
            status='running',
            metadata=self.domain,
        )
        competitor_finder_gen_k8_job.save()
        self.k8_job = competitor_finder_gen_k8_job
        
        create_k8_job(
            competitor_finder_job_id,
            'competitor_finder',
            competitor_finder_job_id,
            self.user.id,
            [competitor_finder_job_id]
        )

    def create_competitor_finder_task_on_flyio(self):
        """
        Creates competitor finder task on Fly.io using Celery
        """
        competitor_finder_job_id = generate_k8_job_id('competitorfinder', username=self.user.username)
        self.competitor_finder_job_id = competitor_finder_job_id

        competitor_finder_data = {
            'domain': self.domain,
            'limit': self.limit,
            'regenerate_competitor': self.regenerate_competitor,
            'abun_webhook_url': reverse('wh-k8-competitor-finder'),        
        }

        competitor_finder_gen_k8_job = KubernetesJob(
            job_id=self.competitor_finder_job_id,
            user=self.user,
            status='running',
            metadata=self.domain,
        )
        competitor_finder_gen_k8_job.save()
        self.k8_job = competitor_finder_gen_k8_job

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(self.competitor_finder_job_id, json.dumps(competitor_finder_data))
            redis_connection.expire(self.competitor_finder_job_id, REDIS_ART_GEN_EXPIRY)

        cmd = f"python3 competitor_finder.py {self.competitor_finder_job_id}"
        cmd = cmd.split()
        worker_props = {
            "config": {
                "image": FLY_COMPETITOR_FINDER_GEN_IMAGE_URL,
                "auto_destroy": True,
                "init": {
                    "cmd": cmd
                },
                "restart": {
                    "policy": "no"
                },
                "guest": {
                    "cpu_kind": "shared",
                    "cpus": 1,
                    "memory_mb": 1024
                }
            },
        }

        res = requests.post(
            f"{FLY_API_HOST}/apps/{FLY_COMPETITOR_FINDER_APP_NAME}/machines",
            headers={
                'Authorization': f"Bearer {FLY_COMPETITOR_FINDER_DEPLOY_TOKEN}",
                'Content-Type': 'application/json'
            },
            json=worker_props
        )

        if res.status_code != 200:
            # Mark the k8 job as failed
            self.k8_job.status = "failed"
            self.k8_job.save()

            logger.error(res.text)
            raise Exception("Failed to send the competitor finder task to fly.io")

        # Get the machine ID and name
        machine_id: str = res.json()['id']
        machine_name: str = res.json()['name']

        # Store it for later use
        self.machine_id = machine_id
        self.machine_name = machine_name

        logger.debug(f"Machine ID: {self.machine_id}")
        logger.debug(f"Machine Name: {self.machine_name}")

        # Use Celery task to check provisioning status
        celery_check_flyio_provisioning.delay(machine_id,
                                              self.competitor_finder_job_id,
                                              FLY_COMPETITOR_FINDER_APP_NAME,
                                              FLY_COMPETITOR_FINDER_DEPLOY_TOKEN)
