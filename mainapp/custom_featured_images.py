from PIL import Image, ImageDraw, ImageFont
import textwrap
import requests
from io import By<PERSON><PERSON>
from typing import Dict
import boto3
import os
import re
import hashlib

from mainapp.models import FeaturedImage

def generate_custom_feature_image(
    photo_url: str,
    logo_url: str,
    article_title: str,
    article_uid: str,
    template_id: str,
    template_text: bool = True,
    images_file_format: str = "png",
    current_template_image_url: str = ""
) -> Dict:
    """
    Generates a custom feature image using the provided inputs.

    Args:
        photo_url (str): The URL of the unsplash image.
        logo_url (str): The URL of the logo image.
        article_title (str): The title of the article.
        article_uid (str): The unique identifier of the article.
        template_id (str): The ID of the template to use.
        template_text (bool): Whether to include text in the template. Defaults to "True".
        images_file_format (str, optional): The file format of the generated image. Defaults to "png".

    Returns:
        Dict: A dictionary containing the generated custom feature image.

    Raises:
        None

    """
    try:
        # --------------------------- Load the images ------------------------
        # load the unsplash image
        unsplash_img = Image.open(BytesIO(requests.get(photo_url).content))
        # load the logo image
        try:
            logo = Image.open(BytesIO(requests.get(logo_url).content))
        except:
            logo = None

        # --------------------------- Get the title text ------------------------
        text = article_title if template_text else "\u200B"

        # Define the canvas size and create a new image
        canvas_width, canvas_height = 1200 * 4, 630 * 4
        canvas = Image.new("RGB", (canvas_width, canvas_height), "#000000")

        # --------------------------- resize the unsplash image ------------------------
        # crop the unsplash image to 1:1 aspect ratio from the center
        width, height = unsplash_img.size
        crop_size = min(width, height)
        left = (width - crop_size) // 2
        top = (height - crop_size) // 2
        right = left + crop_size
        bottom = top + crop_size
        unsplash_img = unsplash_img.crop((left, top, right, bottom))
        # resize the unsplash image to (630 * 4, 630 * 4)
        unsplash_img = unsplash_img.resize((630 * 4, 630 * 4), resample=Image.LANCZOS)

        if template_id == "ok0l2K5mppOLZ3j1Yx":
            # Load the font
            font_path = "mainapp/fonts/AmaticSC-Regular.ttf"
            font_size = 340
            font = ImageFont.truetype(font_path, font_size)
            # Wrap the text
            text = "\n".join(textwrap.wrap(text, width=35, break_long_words=False))

            # Draw the text
            draw = ImageDraw.Draw(canvas)

            # Calculate the bounding box for the text
            text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            # Dynamically reduce font-size if the text is unable to fit in the canvas
            while text_width > canvas_width or text_height > (canvas_height - 100):
                font_size -= 10
                font = ImageFont.truetype(font_path, font_size)
                text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

            # Calculate the position to center the text
            text_x = (canvas_width - text_width) // 2
            text_y = (canvas_height - text_height) // 2

            # Draw the text on the canvas
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill="#F3FF5D",
                align="center",
                spacing=40,
            )

        elif template_id == "j14WwV5Vjj4R5a7XrB":
            # --------------------------- Overlay Polygons --------------------------------
            # create a draw object
            draw = ImageDraw.Draw(canvas)
            # A black rectangle covering the 100% of the unsplash image
            draw.polygon(
                [
                    (0, 0),  # top left
                    ((1200 * 4), 0),  # top right
                    ((1200 * 4), (630 * 4)),  # bottom right
                    (0, (630 * 4)),  # bottom left
                ],
                fill="#000000",
            )

            # --------------------------- Company branding --------------------------------
            if logo:
                # resize or crop the logo to (495, 97) while maintaining the aspect ratio
                # check if the logo is 1:1 aspect ratio
                if logo.size[0] != logo.size[1]:
                    # resize or crop the logo to (495, 97) while maintaining the aspect ratio
                    l = logo.size
                    if l[0] / l[1] > (495 * 4) / (97 * 4):
                        # crop the width
                        logo = logo.crop((0, 0, l[1] * (495 * 4) / (97 * 4), l[1]))
                    else:
                        # crop the height
                        logo = logo.crop((0, 0, l[0], l[0] * (97 * 4) / (495 * 4)))
                else:
                    logo = logo.resize((97 * 4, 97 * 4), resample=Image.LANCZOS)
                # paste the logo on the unsplash image
                canvas.paste(logo, ((33 * 4), (67 * 4)))

            # --------------------------- Pasting Unsplash Image ----------------------------
            # unsplash image with rounded corners
            def add_rounded_corners(im, rad):
                circle = Image.new("L", (rad * 2, rad * 2), 0)
                draw = ImageDraw.Draw(circle)
                draw.ellipse((0, 0, rad * 2 - 1, rad * 2 - 1), fill=255)
                alpha = Image.new("L", im.size, 255)
                w, h = im.size
                alpha.paste(circle.crop((0, 0, rad, rad)), (0, 0))
                alpha.paste(circle.crop((0, rad, rad, rad * 2)), (0, h - rad))
                alpha.paste(circle.crop((rad, 0, rad * 2, rad)), (w - rad, 0))
                alpha.paste(
                    circle.crop((rad, rad, rad * 2, rad * 2)), (w - rad, h - rad)
                )
                im.putalpha(alpha)
                return im

            # Crop the center of the image
            width, height = unsplash_img.size
            crop_size = min(width, height)
            left = (width - crop_size) // 2
            top = (height - crop_size) // 2
            right = left + crop_size
            bottom = top + crop_size
            # Crop and resize the image
            unsplash_img = unsplash_img.crop((left, top, right, bottom)).resize(
                (300 * 4, 300 * 4), resample=Image.LANCZOS
            )
            unsplash_img = add_rounded_corners(unsplash_img, 100)
            canvas.paste(unsplash_img, ((449 * 4), (70 * 4)), unsplash_img)

            # --------------------------- Text Area --------------------------------
            if template_text:
                # define the font
                font_path = "mainapp/fonts/Arial.ttf"
                font_size = 300
                font = ImageFont.truetype(font_path, font_size)
                text = "\n".join(textwrap.wrap(text, width=25, break_long_words=False))
                draw = ImageDraw.Draw(canvas)
                # center the text
                # calculate the text area dimensions
                text_bbox = draw.textbbox(
                    (0, 0), text, font=font, align="center", spacing=40
                )
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                text_x = (canvas.width - text_width) // 2  # center the text on the x-axis
                text_y = 395 * 4  # starting y position
                # dynamically reduce font-size if the text is unable to fit in the canvas
                while text_width > canvas_width or text_height > (canvas_height - 100):
                    font_size -= 10
                    font = ImageFont.truetype(font_path, font_size)
                    text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                draw.text(
                    (text_x, text_y),
                    text,
                    font=font,
                    fill="white",
                    align="center",
                    spacing=80,
                )

        elif template_id == "7wpnPQZzKKOm5dOgxo":
            # paste the unsplash image on the canvas at the left side
            canvas.paste(unsplash_img, (-125 * 4, 0))

            # --------------------------- Overlay Polygons --------------------------------
            # create a draw object
            draw = ImageDraw.Draw(canvas)
            # A light blue rectangle covering the right side unsplash image about 25% of the entire width and 100% of the entire height of the original image
            draw.polygon(
                [
                    ((1150 * 4), 0),  # top left
                    ((1200 * 4), 0),  # top right
                    ((1200 * 4), (630 * 4)),  # bottom right
                    ((1050 * 4), (630 * 4)),  # bottom left
                ],
                fill="#3885ff",
            )
            # A blue parallelogram covering the right side unsplash image about 50% of the width and 100% of the height
            draw.polygon(
                [
                    ((420 * 4), 0),  # top left
                    ((1165 * 4), 0),  # top right
                    ((1045 * 4), (750 * 4)),  # bottom right
                    ((320 * 4), (625 * 4)),  # bottom left
                ],
                fill="#353aff",
            )

            # --------------------------- Company branding --------------------------------
            if logo:
                # check if the logo is 1:1 aspect ratio
                if logo.size[0] != logo.size[1]:
                    # resize or crop the logo to (525, 375) while maintaining the aspect ratio
                    l = logo.size
                    if l[0] / l[1] > 525 / 375:
                        # crop the width
                        logo = logo.crop((0, 0, l[1] * 525 / 375, l[1]))
                    else:
                        # crop the height
                        logo = logo.crop((0, 0, l[0], l[0] * 375 / 525))
                else:
                    logo = logo.resize((384, 384), resample=Image.LANCZOS)
                # paste the logo on the unsplash image
                canvas.paste(logo, ((438 * 4), (127 * 4)))

            # --------------------------- Text Area --------------------------------
            # define the font
            font_path = "mainapp/fonts/AlegreyaSansSC-Light.ttf"
            font_size = 355
            font = ImageFont.truetype(font_path, font_size)
            # create a draw object
            draw = ImageDraw.Draw(canvas)
            # center the text
            text = "\n".join(textwrap.wrap(text, width=20))
            text_x = 385 * 4  # starting x position
            text_y = 250 * 4  # starting y position
            # center the text
            # calculate the text area dimensions
            text_bbox = draw.textbbox(
                (text_x, text_y), text, font=font, align="left", spacing=80
            )
            text_height = text_bbox[3] - text_bbox[1]
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            # dynamically reduce font-size if the text is unable to fit in the canvas
            while text_width > canvas_width or text_height > (canvas_height - 100):
                font_size -= 10
                font = ImageFont.truetype(font_path, font_size)
                text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
            # place the text in the center of the canvas
            text_y = canvas_height // 2 - text_height // 2

            if logo:
                # place the text slightly below the logo
                text_y += 250

            # draw the text on the text area
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill="white",
                align="left",
                spacing=80,
            )

        elif template_id == "yKBqAzZ9xwB0bvMx36":
            # paste the unsplash image on the canvas at the right side
            canvas.paste(unsplash_img, (630 * 4, 0))
            # --------------------------- Overlay Polygons --------------------------------
            # create a draw object
            draw = ImageDraw.Draw(canvas)
            # A white parallelogram covering the left side of the unsplash image about 50% of the width and 100% of the height
            draw.polygon(
                [
                    (0, 0),  # top left
                    (750 * 4, 0),  # top right
                    (650 * 4, 630 * 4),  # bottom right
                    (0, 630 * 4),  # bottom left
                ],
                fill="#ffffff",
            )

            # --------------------------- Company branding --------------------------------
            if logo:
                # check if the logo is 1:1 aspect ratio
                if logo.size[0] != logo.size[1]:
                    # resize or crop the logo to (525, 375) while maintaining the aspect ratio
                    l = logo.size
                    if l[0] / l[1] > 525 / 375:
                        # crop the width
                        logo = logo.crop((0, 0, l[1] * 525 / 375, l[1]))
                    else:
                        # crop the height
                        logo = logo.crop((0, 0, l[0], l[0] * 375 / 525))
                else:
                    logo = logo.resize((384, 384), resample=Image.LANCZOS)
                # paste the logo on the unsplash image
                canvas.paste(logo, ((33 * 4), (29 * 4)))

            # --------------------------- Text Area --------------------------------
            # define the font
            font_path = "mainapp/fonts/WorkSans-ExtraBold.ttf"
            font_size = 340
            font = ImageFont.truetype(font_path, font_size)
            # create a draw object
            draw = ImageDraw.Draw(canvas)
            # center the text
            text = "\n".join(textwrap.wrap(text, width=14))
            text_x = 35 * 4  # starting x position
            # center the text in y-axis
            text_y = 725 * 4  # starting y position

            # center the text
            # calculate the text area dimensions
            text_bbox = draw.textbbox(
                (text_x, text_y), text, font=font, align="left", spacing=40
            )
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            # dynamically reduce font-size if the text is unable to fit in the canvas
            while text_width > canvas_width or text_height > (canvas_height - 100):
                font_size -= 10
                font = ImageFont.truetype(font_path, font_size)
                text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

            # place the text in the center of the canvas
            text_y = canvas_height // 2 - text_height // 2 - font_size // 2

            if logo:
                text_y += 250
            
            # draw the text on the text area
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill="#000",
                align="left",
                spacing=40,
            )

        elif template_id == "kY4Qv7D8dAaLDB0qmP":
            # Load the font
            font_path = "mainapp/fonts/Raleway-ExtraBold.ttf"
            font_size = 60*7
            font = ImageFont.truetype(font_path, font_size)
            canvas = Image.new("RGB", (canvas_width, canvas_height), "#6DFFAE")

            # --------------------------- Text Area --------------------------------
            # Wrap the text
            text = "\n".join(textwrap.wrap(text, width=20, break_long_words=False))

            # Draw the text
            draw = ImageDraw.Draw(canvas)

            # Calculate the bounding box for the text
            text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            # Dynamically reduce font-size if the text is unable to fit in the canvas
            while text_width > canvas_width or text_height > (canvas_height - 100):
                font_size -= 10
                font = ImageFont.truetype(font_path, font_size)
                text_bbox = draw.textbbox((0, 0), text, font=font, align="center", spacing=40)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

            # Calculate the position to center the text
            text_x = (canvas_width - text_width) // 2
            text_y = (canvas_height - text_height) // 2

            # Draw the text on the canvas
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill="black",
                align="center",
                spacing=40,
            )

        # --------------------------- Save the final image ----------------------------
        # Finally, resize the image to (1200, 630) to get the final image
        canvas = canvas.resize((1200, 630), resample=Image.LANCZOS)
        try:
            # uploading canvas to cdn
            # Converting the image to bytes
            image_bytes = BytesIO()
            canvas.save(image_bytes, format=images_file_format, optimize=True, quality=90)
            image_bytes.seek(0)
            # Uploading the image to the CDN (linode object storage)
            linode_obj_config = {
                "aws_access_key_id": os.environ["CLOUDFLARE_R2_ACCESS_KEY"],
                "aws_secret_access_key": os.environ["CLOUDFLARE_R2_SECRET_KEY"],
                "endpoint_url": f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}",
            }
            client = boto3.client("s3", **linode_obj_config)

            try:
                if current_template_image_url and photo_url:
                    featured_image = FeaturedImage.objects.filter(template_image_url=current_template_image_url).first()
                    if featured_image.template_image_url:
                        match = re.search(r'hash-([a-z0-9]+)', featured_image.image_url)                        
                        photo_id = match.group(1)                                                                       
                        delete_key = f"{article_uid}_hash-{photo_id}.{images_file_format}"                                                
                        client.delete_object(
                                Bucket=os.environ["CLOUDFLARE_R2_BUCKET_NAME"],
                                Key=delete_key,
                            )
            except Exception as e:
                print(f"An error occurred error: {e}")

            image_content = image_bytes.read()
            image_hash = hashlib.sha256(image_content).hexdigest()
            image_bytes.seek(0)
            new_key = f"{article_uid}_hash-{image_hash}.{images_file_format}"

            extra_args = {
                "ACL": "public-read",
                "ContentType": "image/png"
            }

            client.upload_fileobj(
                image_bytes,
                os.environ["CLOUDFLARE_R2_BUCKET_NAME"],
                new_key,
                ExtraArgs=extra_args
            )
            
            # making the image public
            # client.put_object_acl(
            #         ACL="public-read",
            #         Bucket=os.environ["CLOUDFLARE_R2_BUCKET_NAME"],
            #         Key=article_uid + "_" + photo_id + "." + images_file_format,
            #     )

            # Getting the URL link of the image stored in the linode object storage
            return {
                "image_url": f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}/{os.environ['CLOUDFLARE_R2_BUCKET_NAME']}/{new_key}"
            }
        except Exception as e:
            raise e
    except Exception as e:
        # Handle any exceptions that occur during image generation
        print(e)
        return {"status": "error", "err_id": "FAILED_TO_CREATE_OWN_FEATURE_IMAGE", "image_url": None}
